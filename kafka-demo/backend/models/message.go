package models

import "time"

// Message 表示 Kafka 消息结构
type Message struct {
	ID        string    `json:"id"`
	Topic     string    `json:"topic"`
	Key       string    `json:"key"`
	Value     string    `json:"value"`
	Partition int       `json:"partition"`
	Offset    int64     `json:"offset"`
	Timestamp time.Time `json:"timestamp"`
	Type      string    `json:"type"` // "produced" 或 "consumed"
}

// ProduceRequest 生产消息请求
type ProduceRequest struct {
	Topic string `json:"topic" binding:"required"`
	Key   string `json:"key"`
	Value string `json:"value" binding:"required"`
}

// ConsumeRequest 消费消息请求
type ConsumeRequest struct {
	Topic     string `json:"topic" binding:"required"`
	GroupID   string `json:"groupId"`
	Partition int    `json:"partition"`
}

// Response 通用响应结构
type Response struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// WebSocketMessage WebSocket 消息结构
type WebSocketMessage struct {
	Type string      `json:"type"` // "message", "status", "error"
	Data interface{} `json:"data"`
}
