package handlers

import (
	"fmt"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"kafka-demo/config"
	"kafka-demo/kafka"
	"kafka-demo/models"
	"kafka-demo/utils"
)

type KafkaHandler struct {
	producer    *kafka.Producer
	consumers   map[string]*kafka.Consumer
	consumersMu sync.RWMutex
	clients     map[*websocket.Conn]bool
	clientsMu   sync.RWMutex
	upgrader    websocket.Upgrader
	messages    []models.Message
	messagesMu  sync.RWMutex
	config      *config.Config
	brokers     []string
}

// NewKafkaHandler 创建新的 Kafka 处理器
func NewKafkaHandler(brokers []string, cfg *config.Config) *KafkaHandler {
	producer := kafka.NewProducer(brokers, &cfg.Kafka.ProducerConfig)

	return &KafkaHandler{
		producer:  producer,
		consumers: make(map[string]*kafka.Consumer),
		clients:   make(map[*websocket.Conn]bool),
		upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				return true // 允许跨域
			},
			ReadBufferSize:  1024,
			WriteBufferSize: 1024,
		},
		messages: make([]models.Message, 0),
		config:   cfg,
		brokers:  brokers,
	}
}

// ProduceMessage 生产消息
func (h *KafkaHandler) ProduceMessage(c *gin.Context) {
	var req models.ProduceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.GlobalLogger.Warn("Invalid produce request: %v", err)
		c.Error(utils.NewAppError(http.StatusBadRequest, "请求参数无效", err.Error()))
		return
	}

	utils.GlobalLogger.Debug("Producing message to topic %s: %s", req.Topic, req.Value)

	err := h.producer.ProduceMessage(req.Topic, req.Key, req.Value)
	if err != nil {
		utils.GlobalLogger.Error("Failed to produce message: %v", err)
		c.Error(utils.WrapError(err, "消息生产失败"))
		return
	}

	// 创建消息记录
	message := models.Message{
		ID:        fmt.Sprintf("prod-%d", time.Now().UnixNano()),
		Topic:     req.Topic,
		Key:       req.Key,
		Value:     req.Value,
		Timestamp: time.Now(),
		Type:      "produced",
	}

	// 保存消息到历史记录
	h.addMessageToHistory(message)

	// 广播消息到所有 WebSocket 客户端
	h.broadcastMessage(models.WebSocketMessage{
		Type: "message",
		Data: message,
	})

	utils.GlobalLogger.Info("Message produced successfully to topic %s", req.Topic)

	c.JSON(http.StatusOK, models.Response{
		Success: true,
		Message: "消息发送成功",
		Data:    message,
	})
}

// addMessageToHistory 添加消息到历史记录
func (h *KafkaHandler) addMessageToHistory(message models.Message) {
	h.messagesMu.Lock()
	defer h.messagesMu.Unlock()

	h.messages = append(h.messages, message)
	maxHistory := h.config.Kafka.MaxMessageHistory
	if len(h.messages) > maxHistory {
		h.messages = h.messages[len(h.messages)-maxHistory:]
	}
}

// StartConsumer 开始消费消息
func (h *KafkaHandler) StartConsumer(c *gin.Context) {
	var req models.ConsumeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.GlobalLogger.Warn("Invalid consume request: %v", err)
		c.Error(utils.NewAppError(http.StatusBadRequest, "请求参数无效", err.Error()))
		return
	}

	consumerKey := fmt.Sprintf("%s-%s", req.Topic, req.GroupID)

	h.consumersMu.Lock()
	if _, exists := h.consumers[consumerKey]; exists {
		h.consumersMu.Unlock()
		utils.GlobalLogger.Warn("Consumer already exists: %s", consumerKey)
		c.Error(utils.ErrConsumerExists)
		return
	}

	consumer := kafka.NewConsumer(h.brokers, req.Topic, req.GroupID, &h.config.Kafka.ConsumerConfig)
	h.consumers[consumerKey] = consumer
	h.consumersMu.Unlock()

	consumer.Start()

	// 监听消费的消息
	go h.handleConsumedMessages(consumer, consumerKey)

	utils.GlobalLogger.Info("Consumer started: %s", consumerKey)

	c.JSON(http.StatusOK, models.Response{
		Success: true,
		Message: "消费者启动成功",
		Data: map[string]string{
			"consumerKey": consumerKey,
			"topic":       req.Topic,
			"groupId":     req.GroupID,
		},
	})
}

// StopConsumer 停止消费者
func (h *KafkaHandler) StopConsumer(c *gin.Context) {
	var req struct {
		ConsumerKey string `json:"consumerKey" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		utils.GlobalLogger.Warn("Invalid stop consumer request: %v", err)
		c.Error(utils.NewAppError(http.StatusBadRequest, "请求参数无效", err.Error()))
		return
	}

	h.consumersMu.Lock()
	consumer, exists := h.consumers[req.ConsumerKey]
	if !exists {
		h.consumersMu.Unlock()
		utils.GlobalLogger.Warn("Consumer not found: %s", req.ConsumerKey)
		c.Error(utils.ErrConsumerNotFound)
		return
	}

	delete(h.consumers, req.ConsumerKey)
	h.consumersMu.Unlock()

	consumer.Close()
	utils.GlobalLogger.Info("Consumer stopped: %s", req.ConsumerKey)

	c.JSON(http.StatusOK, models.Response{
		Success: true,
		Message: "消费者已停止",
	})
}

// GetConsumers 获取消费者状态
func (h *KafkaHandler) GetConsumers(c *gin.Context) {
	h.consumersMu.RLock()
	consumers := make([]map[string]interface{}, 0, len(h.consumers))
	for key := range h.consumers {
		parts := strings.Split(key, "-")
		if len(parts) >= 2 {
			consumers = append(consumers, map[string]interface{}{
				"consumerKey": key,
				"topic":       parts[0],
				"groupId":     strings.Join(parts[1:], "-"),
				"status":      "running",
			})
		}
	}
	h.consumersMu.RUnlock()

	c.JSON(http.StatusOK, models.Response{
		Success: true,
		Message: "获取消费者状态成功",
		Data:    consumers,
	})
}

// handleConsumedMessages 处理消费的消息
func (h *KafkaHandler) handleConsumedMessages(consumer *kafka.Consumer, consumerKey string) {
	messageChan := consumer.GetMessageChannel()
	for message := range messageChan {
		utils.GlobalLogger.Debug("Message consumed from %s: %s", message.Topic, message.Value)

		// 保存消息到历史记录
		h.addMessageToHistory(message)

		// 广播消息到所有 WebSocket 客户端
		h.broadcastMessage(models.WebSocketMessage{
			Type: "message",
			Data: message,
		})
	}
	utils.GlobalLogger.Info("Consumer message handler stopped: %s", consumerKey)
}

// GetMessages 获取消息历史
func (h *KafkaHandler) GetMessages(c *gin.Context) {
	h.messagesMu.RLock()
	messages := make([]models.Message, len(h.messages))
	copy(messages, h.messages)
	h.messagesMu.RUnlock()

	c.JSON(http.StatusOK, models.Response{
		Success: true,
		Message: "获取消息历史成功",
		Data:    messages,
	})
}

// WebSocketHandler WebSocket 连接处理
func (h *KafkaHandler) WebSocketHandler(c *gin.Context) {
	conn, err := h.upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		utils.GlobalLogger.Error("WebSocket upgrade failed: %v", err)
		c.Error(utils.ErrWebSocketUpgrade)
		return
	}
	defer conn.Close()

	clientIP := c.ClientIP()
	utils.GlobalLogger.Info("WebSocket client connected: %s", clientIP)

	h.clientsMu.Lock()
	h.clients[conn] = true
	h.clientsMu.Unlock()

	defer func() {
		h.clientsMu.Lock()
		delete(h.clients, conn)
		h.clientsMu.Unlock()
		utils.GlobalLogger.Info("WebSocket client disconnected: %s", clientIP)
	}()

	// 发送当前消息历史
	h.messagesMu.RLock()
	for _, message := range h.messages {
		if err := conn.WriteJSON(models.WebSocketMessage{
			Type: "message",
			Data: message,
		}); err != nil {
			utils.GlobalLogger.Error("Failed to send message history: %v", err)
			break
		}
	}
	h.messagesMu.RUnlock()

	// 设置读取超时
	conn.SetReadDeadline(time.Now().Add(60 * time.Second))
	conn.SetPongHandler(func(string) error {
		conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		return nil
	})

	// 启动 ping 协程
	go func() {
		ticker := time.NewTicker(30 * time.Second)
		defer ticker.Stop()
		for {
			select {
			case <-ticker.C:
				if err := conn.WriteMessage(websocket.PingMessage, nil); err != nil {
					return
				}
			}
		}
	}()

	// 保持连接活跃
	for {
		_, _, err := conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				utils.GlobalLogger.Error("WebSocket error: %v", err)
			}
			break
		}
	}
}

// broadcastMessage 广播消息到所有客户端
func (h *KafkaHandler) broadcastMessage(message models.WebSocketMessage) {
	h.clientsMu.Lock()
	defer h.clientsMu.Unlock()

	for client := range h.clients {
		err := client.WriteJSON(message)
		if err != nil {
			utils.GlobalLogger.Error("WebSocket write error: %v", err)
			client.Close()
			delete(h.clients, client)
		}
	}
}

// Close 关闭 Kafka 处理器
func (h *KafkaHandler) Close() {
	utils.GlobalLogger.Info("Closing Kafka handler...")

	// 关闭所有消费者
	h.consumersMu.Lock()
	for key, consumer := range h.consumers {
		utils.GlobalLogger.Info("Closing consumer: %s", key)
		consumer.Close()
	}
	h.consumers = make(map[string]*kafka.Consumer)
	h.consumersMu.Unlock()

	// 关闭生产者
	if h.producer != nil {
		utils.GlobalLogger.Info("Closing producer")
		h.producer.Close()
	}

	// 关闭所有 WebSocket 连接
	h.clientsMu.Lock()
	for client := range h.clients {
		client.Close()
	}
	h.clients = make(map[*websocket.Conn]bool)
	h.clientsMu.Unlock()

	utils.GlobalLogger.Info("Kafka handler closed")
}
