package kafka

import (
	"context"
	"fmt"
	"time"

	"github.com/segmentio/kafka-go"
	"kafka-demo/config"
	"kafka-demo/models"
	"kafka-demo/utils"
)

type Consumer struct {
	reader      *kafka.Reader
	messageChan chan models.Message
	stopChan    chan bool
	topic       string
	groupID     string
}

// NewConsumer 创建新的 Kafka 消费者
func NewConsumer(brokers []string, topic, groupID string, cfg *config.ConsumerConfig) *Consumer {
	reader := kafka.NewReader(kafka.ReaderConfig{
		Brokers:        brokers,
		Topic:          topic,
		GroupID:        groupID,
		MinBytes:       cfg.MinBytes,
		MaxBytes:       cfg.MaxBytes,
		CommitInterval: time.Duration(cfg.CommitInterval) * time.Second,
		StartOffset:    kafka.LastOffset,
		MaxWait:        1 * time.Second,
	})

	utils.GlobalLogger.Info("Kafka consumer created for topic %s, group %s", topic, groupID)

	return &Consumer{
		reader:      reader,
		messageChan: make(chan models.Message, 100),
		stopChan:    make(chan bool),
		topic:       topic,
		groupID:     groupID,
	}
}

// Start 开始消费消息
func (c *Consumer) Start() {
	utils.GlobalLogger.Info("Starting consumer for topic %s, group %s", c.topic, c.groupID)

	go func() {
		defer func() {
			if r := recover(); r != nil {
				utils.GlobalLogger.Error("Consumer panic recovered: %v", r)
			}
		}()

		for {
			select {
			case <-c.stopChan:
				utils.GlobalLogger.Info("Consumer stopped for topic %s, group %s", c.topic, c.groupID)
				return
			default:
				ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
				message, err := c.reader.ReadMessage(ctx)
				cancel()

				if err != nil {
					utils.GlobalLogger.Warn("Error reading message from topic %s: %v", c.topic, err)
					time.Sleep(1 * time.Second) // 避免快速重试
					continue
				}

				msg := models.Message{
					ID:        fmt.Sprintf("%s-%d-%d", c.topic, message.Partition, message.Offset),
					Topic:     message.Topic,
					Key:       string(message.Key),
					Value:     string(message.Value),
					Partition: message.Partition,
					Offset:    message.Offset,
					Timestamp: message.Time,
					Type:      "consumed",
				}

				select {
				case c.messageChan <- msg:
					utils.GlobalLogger.Debug("Message consumed from topic %s: %s", message.Topic, string(message.Value))
				default:
					utils.GlobalLogger.Warn("Message channel is full, dropping message from topic %s", message.Topic)
				}
			}
		}
	}()
}

// GetMessageChannel 获取消息通道
func (c *Consumer) GetMessageChannel() <-chan models.Message {
	return c.messageChan
}

// Stop 停止消费者
func (c *Consumer) Stop() {
	select {
	case <-c.stopChan:
		// 已经停止
	default:
		close(c.stopChan)
	}
}

// Close 关闭消费者
func (c *Consumer) Close() error {
	utils.GlobalLogger.Info("Closing consumer for topic %s, group %s", c.topic, c.groupID)

	c.Stop()

	// 等待一小段时间让消费者协程退出
	time.Sleep(100 * time.Millisecond)

	close(c.messageChan)

	if err := c.reader.Close(); err != nil {
		utils.GlobalLogger.Error("Error closing consumer reader: %v", err)
		return err
	}

	utils.GlobalLogger.Info("Consumer closed for topic %s, group %s", c.topic, c.groupID)
	return nil
}
