package kafka

import (
	"context"
	"fmt"
	"time"

	"github.com/segmentio/kafka-go"
	"kafka-demo/config"
	"kafka-demo/utils"
)

type Producer struct {
	writer *kafka.Writer
}

// NewProducer 创建新的 Kafka 生产者
func NewProducer(brokers []string, cfg *config.ProducerConfig) *Producer {
	writer := &kafka.Writer{
		Addr:         kafka.TCP(brokers...),
		Balancer:     &kafka.LeastBytes{},
		BatchTimeout: time.Duration(cfg.BatchTimeout) * time.Millisecond,
		BatchSize:    cfg.BatchSize,
		RequiredAcks: kafka.RequireOne,
		Async:        false,
	}

	utils.GlobalLogger.Info("Kafka producer created with brokers: %v", brokers)

	return &Producer{
		writer: writer,
	}
}

// ProduceMessage 发送消息到 Kafka
func (p *Producer) ProduceMessage(topic, key, value string) error {
	message := kafka.Message{
		Topic: topic,
		Key:   []byte(key),
		Value: []byte(value),
		Time:  time.Now(),
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	err := p.writer.WriteMessages(ctx, message)
	if err != nil {
		utils.GlobalLogger.Error("Failed to produce message to topic %s: %v", topic, err)
		return fmt.Errorf("failed to produce message to topic %s: %w", topic, err)
	}

	utils.GlobalLogger.Debug("Message produced successfully to topic %s", topic)
	return nil
}

// Close 关闭生产者
func (p *Producer) Close() error {
	utils.GlobalLogger.Info("Closing Kafka producer")
	return p.writer.Close()
}
