package utils

import (
	"fmt"
	"log"
	"os"
	"time"
)

// LogLevel 日志级别
type LogLevel int

const (
	DEBUG LogLevel = iota
	INFO
	WARN
	ERROR
)

// Logger 日志记录器
type Logger struct {
	level  LogLevel
	logger *log.Logger
}

// NewLogger 创建新的日志记录器
func NewLogger(level LogLevel) *Logger {
	return &Logger{
		level:  level,
		logger: log.New(os.Stdout, "", 0),
	}
}

// formatMessage 格式化日志消息
func (l *Logger) formatMessage(level string, message string) string {
	timestamp := time.Now().Format("2006-01-02 15:04:05")
	return fmt.Sprintf("[%s] %s - %s", timestamp, level, message)
}

// Debug 调试日志
func (l *Logger) Debug(format string, args ...interface{}) {
	if l.level <= DEBUG {
		message := fmt.Sprintf(format, args...)
		l.logger.Println(l.formatMessage("DEBUG", message))
	}
}

// Info 信息日志
func (l *Logger) Info(format string, args ...interface{}) {
	if l.level <= INFO {
		message := fmt.Sprintf(format, args...)
		l.logger.Println(l.formatMessage("INFO", message))
	}
}

// Warn 警告日志
func (l *Logger) Warn(format string, args ...interface{}) {
	if l.level <= WARN {
		message := fmt.Sprintf(format, args...)
		l.logger.Println(l.formatMessage("WARN", message))
	}
}

// Error 错误日志
func (l *Logger) Error(format string, args ...interface{}) {
	if l.level <= ERROR {
		message := fmt.Sprintf(format, args...)
		l.logger.Println(l.formatMessage("ERROR", message))
	}
}

// 全局日志记录器
var GlobalLogger = NewLogger(INFO)
