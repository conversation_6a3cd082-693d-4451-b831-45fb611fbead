package utils

import (
	"fmt"
	"net/http"
)

// AppError 应用错误
type AppError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

// Error 实现 error 接口
func (e *AppError) Error() string {
	return fmt.Sprintf("Code: %d, Message: %s, Details: %s", e.Code, e.Message, e.Details)
}

// NewAppError 创建新的应用错误
func NewAppError(code int, message string, details ...string) *AppError {
	var detail string
	if len(details) > 0 {
		detail = details[0]
	}
	return &AppError{
		Code:    code,
		Message: message,
		Details: detail,
	}
}

// 预定义错误
var (
	ErrInvalidRequest = NewAppError(http.StatusBadRequest, "请求参数无效")
	ErrKafkaConnection = NewAppError(http.StatusInternalServerError, "Kafka 连接失败")
	ErrProduceFailed = NewAppError(http.StatusInternalServerError, "消息生产失败")
	ErrConsumeFailed = NewAppError(http.StatusInternalServerError, "消息消费失败")
	ErrWebSocketUpgrade = NewAppError(http.StatusBadRequest, "WebSocket 升级失败")
	ErrConsumerExists = NewAppError(http.StatusConflict, "消费者已存在")
	ErrConsumerNotFound = NewAppError(http.StatusNotFound, "消费者不存在")
)

// WrapError 包装错误
func WrapError(err error, message string) *AppError {
	if appErr, ok := err.(*AppError); ok {
		return appErr
	}
	return NewAppError(http.StatusInternalServerError, message, err.Error())
}
