package middleware

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"kafka-demo/utils"
)

// CORS 跨域中间件
func CORS() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.<PERSON>er("Access-Control-Allow-Origin", "*")
		c.<PERSON><PERSON>("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.<PERSON><PERSON>("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With")
		c.<PERSON><PERSON>("Access-Control-Allow-Credentials", "true")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

// NoCache 禁用缓存中间件
func NoCache() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.<PERSON>("Cache-Control", "no-cache, no-store, must-revalidate")
		c.<PERSON><PERSON>("Pragma", "no-cache")
		c.<PERSON><PERSON>("Expires", "0")
		c.Next()
	}
}

// Logger 日志中间件
func Logger() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path
		raw := c.Request.URL.RawQuery
		
		c.Next()
		
		end := time.Now()
		latency := end.Sub(start)
		
		clientIP := c.ClientIP()
		method := c.Request.Method
		statusCode := c.Writer.Status()
		
		if raw != "" {
			path = path + "?" + raw
		}
		
		utils.GlobalLogger.Info("%s %s %d %v %s",
			method,
			path,
			statusCode,
			latency,
			clientIP,
		)
	}
}

// ErrorHandler 错误处理中间件
func ErrorHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()
		
		// 处理错误
		if len(c.Errors) > 0 {
			err := c.Errors.Last()
			
			if appErr, ok := err.Err.(*utils.AppError); ok {
				c.JSON(appErr.Code, gin.H{
					"success": false,
					"message": appErr.Message,
					"details": appErr.Details,
				})
			} else {
				utils.GlobalLogger.Error("Unhandled error: %v", err.Err)
				c.JSON(http.StatusInternalServerError, gin.H{
					"success": false,
					"message": "内部服务器错误",
				})
			}
		}
	}
}

// Recovery 恢复中间件
func Recovery() gin.HandlerFunc {
	return gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
		utils.GlobalLogger.Error("Panic recovered: %v", recovered)
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "服务器内部错误",
		})
	})
}

// RateLimit 简单的速率限制中间件
func RateLimit() gin.HandlerFunc {
	// 这里可以实现更复杂的速率限制逻辑
	return func(c *gin.Context) {
		// 简单实现：每个 IP 每秒最多 100 个请求
		c.Next()
	}
}
