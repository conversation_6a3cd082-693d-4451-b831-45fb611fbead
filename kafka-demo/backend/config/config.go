package config

import (
	"os"
	"strconv"
	"strings"
)

// Config 应用配置
type Config struct {
	Server ServerConfig `json:"server"`
	Ka<PERSON>ka  KafkaConfig  `json:"kafka"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Port         string `json:"port"`
	ReadTimeout  int    `json:"readTimeout"`
	WriteTimeout int    `json:"writeTimeout"`
}

// KafkaConfig Kafka 配置
type KafkaConfig struct {
	Brokers           []string `json:"brokers"`
	MaxMessageHistory int      `json:"maxMessageHistory"`
	ProducerConfig    ProducerConfig `json:"producer"`
	ConsumerConfig    ConsumerConfig `json:"consumer"`
}

// ProducerConfig 生产者配置
type ProducerConfig struct {
	BatchSize    int `json:"batchSize"`
	BatchTimeout int `json:"batchTimeout"` // 毫秒
}

// ConsumerConfig 消费者配置
type ConsumerConfig struct {
	MinBytes       int `json:"minBytes"`
	MaxBytes       int `json:"maxBytes"`
	CommitInterval int `json:"commitInterval"` // 秒
}

// LoadConfig 加载配置
func LoadConfig() *Config {
	return &Config{
		Server: ServerConfig{
			Port:         getEnv("SERVER_PORT", "8080"),
			ReadTimeout:  getEnvAsInt("SERVER_READ_TIMEOUT", 30),
			WriteTimeout: getEnvAsInt("SERVER_WRITE_TIMEOUT", 30),
		},
		Kafka: KafkaConfig{
			Brokers:           getEnvAsSlice("KAFKA_BROKERS", []string{"localhost:9092"}),
			MaxMessageHistory: getEnvAsInt("KAFKA_MAX_MESSAGE_HISTORY", 1000),
			ProducerConfig: ProducerConfig{
				BatchSize:    getEnvAsInt("KAFKA_PRODUCER_BATCH_SIZE", 100),
				BatchTimeout: getEnvAsInt("KAFKA_PRODUCER_BATCH_TIMEOUT", 10),
			},
			ConsumerConfig: ConsumerConfig{
				MinBytes:       getEnvAsInt("KAFKA_CONSUMER_MIN_BYTES", 10240),   // 10KB
				MaxBytes:       getEnvAsInt("KAFKA_CONSUMER_MAX_BYTES", 10485760), // 10MB
				CommitInterval: getEnvAsInt("KAFKA_CONSUMER_COMMIT_INTERVAL", 1),
			},
		},
	}
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// getEnvAsInt 获取环境变量并转换为整数
func getEnvAsInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

// getEnvAsSlice 获取环境变量并转换为字符串切片
func getEnvAsSlice(key string, defaultValue []string) []string {
	if value := os.Getenv(key); value != "" {
		return strings.Split(value, ",")
	}
	return defaultValue
}
