# Kafka 生产消费演示项目

这是一个使用 Go + Gin + Vue 构建的 Kafka 生产消费过程演示项目。

## 项目结构

```
kafka-demo/
├── backend/                 # Go 后端服务
│   ├── handlers/           # HTTP 处理器
│   │   └── kafka_handler.go
│   ├── kafka/              # Kafka 相关代码
│   │   ├── producer.go     # 生产者
│   │   └── consumer.go     # 消费者
│   ├── models/             # 数据模型
│   │   └── message.go
│   ├── main.go             # 主程序入口
│   └── go.mod              # Go 模块文件
├── frontend/               # Vue 前端应用
│   ├── src/
│   │   └── app.js          # 主应用逻辑
│   └── index.html          # 主页面
└── README.md               # 项目说明
```

## 功能特性

### 后端功能
- **Kafka 生产者**: 发送消息到指定主题
- **Kafka 消费者**: 从指定主题消费消息
- **WebSocket 支持**: 实时推送消息到前端
- **RESTful API**: 提供生产、消费、查询接口
- **消息历史**: 保存最近 1000 条消息记录

### 前端功能
- **实时消息展示**: 通过 WebSocket 实时显示消息
- **生产者界面**: 可视化发送消息
- **消费者管理**: 启动和管理消费者
- **统计信息**: 显示消息统计数据
- **响应式设计**: 适配不同屏幕尺寸

## API 接口

### 1. 生产消息
```
POST /api/produce
Content-Type: application/json

{
    "topic": "test-topic",
    "key": "message-key",
    "value": "消息内容"
}
```

### 2. 开始消费
```
POST /api/consume
Content-Type: application/json

{
    "topic": "test-topic",
    "groupId": "test-group"
}
```

### 3. 获取消息历史
```
GET /api/messages
```

### 4. WebSocket 连接
```
GET /api/ws
```

### 5. 健康检查
```
GET /health
```

## 运行要求

### 环境依赖
1. **Go 1.21+**
2. **Apache Kafka** (本地运行在 localhost:9092)
3. **现代浏览器** (支持 WebSocket 和 ES6)

### Kafka 安装和启动

#### 使用 Docker (推荐)
```bash
# 创建 docker-compose.yml
version: '3'
services:
  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000

  kafka:
    image: confluentinc/cp-kafka:latest
    depends_on:
      - zookeeper
    ports:
      - 9092:9092
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1

# 启动 Kafka
docker-compose up -d
```

#### 手动安装
1. 下载 Kafka: https://kafka.apache.org/downloads
2. 启动 Zookeeper: `bin/zookeeper-server-start.sh config/zookeeper.properties`
3. 启动 Kafka: `bin/kafka-server-start.sh config/server.properties`

## 运行步骤

### 1. 启动后端服务
```bash
cd backend
go mod tidy
go run main.go
```

服务将在 http://localhost:8080 启动

### 2. 访问前端界面
打开浏览器访问: http://localhost:8080

## 使用说明

### 发送消息
1. 在"消息生产者"区域填写主题名称和消息内容
2. 点击"发送消息"按钮
3. 消息将显示在实时消息流中

### 消费消息
1. 在"消息消费者"区域填写主题名称和消费组 ID
2. 点击"开始消费"按钮
3. 消费到的消息将实时显示在消息流中

### 实时监控
- WebSocket 连接状态显示在页面顶部
- 统计信息实时更新
- 消息按时间倒序显示，最新消息在顶部

## 技术栈

### 后端
- **Go**: 编程语言
- **Gin**: Web 框架
- **gorilla/websocket**: WebSocket 支持
- **segmentio/kafka-go**: Kafka 客户端

### 前端
- **Vue 3**: 前端框架
- **Element Plus**: UI 组件库
- **WebSocket**: 实时通信

## 注意事项

1. **Kafka 连接**: 确保 Kafka 服务运行在 localhost:9092
2. **主题创建**: Kafka 会自动创建不存在的主题
3. **消费者组**: 相同消费者组的消费者会分担消息
4. **消息持久化**: 消息历史仅保存在内存中，重启后会丢失
5. **并发安全**: 代码已处理并发访问安全问题

## 扩展功能

可以考虑添加的功能：
- 消息持久化到数据库
- 支持多个 Kafka 集群
- 消息过滤和搜索
- 性能监控和指标
- 用户认证和权限管理
- 消息格式验证
- 批量消息处理

## 故障排除

### 常见问题
1. **连接 Kafka 失败**: 检查 Kafka 服务是否启动
2. **WebSocket 连接失败**: 检查防火墙和代理设置
3. **消息不显示**: 检查浏览器控制台错误信息
4. **端口冲突**: 修改 main.go 中的端口配置
