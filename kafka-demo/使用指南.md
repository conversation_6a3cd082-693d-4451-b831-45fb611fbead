# Kafka 生产消费演示 - 使用指南

## 快速开始

### 第一步：启动 Kafka 环境
1. 双击运行 `start-kafka.bat`
2. 等待 Docker 下载并启动 Kafka 服务
3. 看到 "Kafka 环境已就绪" 提示后继续

### 第二步：启动演示应用
1. 双击运行 `start.bat`
2. 等待 Go 依赖下载完成
3. 看到服务启动信息后，打开浏览器访问 http://localhost:8080

## 功能演示

### 1. 发送消息
- 在左侧"消息生产者"区域
- 填写主题名称（如：test-topic）
- 填写消息内容
- 点击"发送消息"
- 观察右侧消息流中出现绿色边框的"生产"消息

### 2. 消费消息
- 在右侧"消息消费者"区域
- 填写相同的主题名称
- 填写消费组 ID（如：test-group）
- 点击"开始消费"
- 观察消息流中出现蓝色边框的"消费"消息

### 3. 实时监控
- 页面顶部显示 WebSocket 连接状态
- 统计信息实时更新：总消息数、已生产、已消费、活跃消费者
- 消息按时间倒序显示，最新消息在顶部

## 测试场景

### 场景1：基本生产消费
1. 启动一个消费者监听 "test-topic"
2. 发送几条消息到 "test-topic"
3. 观察消息被实时消费

### 场景2：多消费者负载均衡
1. 启动多个相同消费组的消费者
2. 发送多条消息
3. 观察消息在消费者间分配

### 场景3：不同消费组
1. 启动不同消费组的消费者
2. 发送消息
3. 观察每个消费组都能收到相同消息

## 管理界面

### Kafka UI (可选)
- 访问 http://localhost:8081
- 查看主题、分区、消费者组等信息
- 监控 Kafka 集群状态

## 故障排除

### 问题1：Kafka 连接失败
**现象**: 发送消息时提示连接失败
**解决**: 
1. 检查 Docker 是否正常运行
2. 运行 `docker-compose ps` 查看服务状态
3. 重新运行 `start-kafka.bat`

### 问题2：WebSocket 连接断开
**现象**: 页面显示 WebSocket 未连接
**解决**:
1. 刷新页面
2. 检查后端服务是否正常运行
3. 查看浏览器控制台错误信息

### 问题3：消息不显示
**现象**: 发送消息后界面无反应
**解决**:
1. 检查浏览器控制台是否有错误
2. 确认主题名称填写正确
3. 检查 Kafka 服务状态

### 问题4：端口冲突
**现象**: 启动时提示端口被占用
**解决**:
1. 关闭占用端口的程序
2. 或修改 `main.go` 中的端口配置

## 高级用法

### 自定义配置
- 修改 `main.go` 中的 Kafka broker 地址
- 调整消息历史记录数量限制
- 修改 WebSocket 重连间隔

### 扩展功能
- 添加消息过滤功能
- 实现消息持久化
- 增加性能监控指标

## 技术细节

### 消息流程
1. 前端发送 HTTP 请求到后端
2. 后端调用 Kafka 生产者发送消息
3. Kafka 消费者接收消息
4. 通过 WebSocket 推送到前端显示

### 数据结构
```json
{
    "id": "消息唯一标识",
    "topic": "主题名称",
    "key": "消息键",
    "value": "消息内容",
    "partition": "分区号",
    "offset": "偏移量",
    "timestamp": "时间戳",
    "type": "produced|consumed"
}
```

### 并发安全
- 使用 sync.RWMutex 保护共享数据
- WebSocket 连接池管理
- Kafka 客户端线程安全

## 学习要点

### Kafka 概念
- **主题 (Topic)**: 消息分类
- **分区 (Partition)**: 主题的子集，提供并行处理
- **消费者组 (Consumer Group)**: 消费者的逻辑分组
- **偏移量 (Offset)**: 消息在分区中的位置

### Go 技术栈
- **Gin**: 轻量级 Web 框架
- **gorilla/websocket**: WebSocket 实现
- **segmentio/kafka-go**: Kafka 客户端库

### Vue 技术栈
- **Vue 3**: 响应式前端框架
- **Element Plus**: UI 组件库
- **WebSocket API**: 浏览器原生 WebSocket

## 下一步

1. 尝试修改代码添加新功能
2. 学习 Kafka 的高级特性
3. 了解微服务架构中的消息队列应用
4. 探索其他消息队列系统（RabbitMQ、Redis Streams 等）
