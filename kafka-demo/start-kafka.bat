@echo off
echo ================================
echo Kafka 环境启动脚本
echo ================================
echo.

echo 检查 Docker 环境...
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到 Docker 环境，请先安装 Docker Desktop
    echo 下载地址: https://www.docker.com/products/docker-desktop
    pause
    exit /b 1
)

echo Docker 环境检查通过
echo.

echo 启动 Kafka 环境...
echo 这可能需要几分钟时间下载镜像...
echo.

docker-compose up -d

if %errorlevel% neq 0 (
    echo 错误: Kafka 启动失败
    pause
    exit /b 1
)

echo.
echo ================================
echo Kafka 环境启动成功！
echo ================================
echo.
echo 服务信息:
echo - Kafka Broker: localhost:9092
echo - Zookeeper: localhost:2181
echo - Kafka UI: http://localhost:8081
echo.
echo 等待服务完全启动...
timeout /t 10 /nobreak >nul

echo.
echo 检查服务状态...
docker-compose ps

echo.
echo Kafka 环境已就绪，可以启动应用程序了！
echo 运行 start.bat 启动演示应用
echo.
pause
