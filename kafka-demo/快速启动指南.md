# Kafka 演示项目 - 快速启动指南

## 🚀 一键启动

### 方法一：使用批处理脚本（推荐）

1. **启动 Kafka 环境**
   ```bash
   双击运行 start-kafka.bat
   ```
   - 等待 Docker 下载并启动 Kafka 服务
   - 看到 "Kafka 环境已就绪" 提示

2. **启动演示应用**
   ```bash
   双击运行 start.bat
   ```
   - 自动下载 Go 依赖
   - 启动后端服务

3. **访问应用**
   - 打开浏览器访问：http://localhost:8080
   - 开始体验 Kafka 生产消费过程

### 方法二：手动启动

1. **启动 Kafka**
   ```bash
   cd kafka-demo
   docker-compose up -d
   ```

2. **启动后端**
   ```bash
   cd backend
   go mod tidy
   go run main.go
   ```

3. **访问前端**
   - 浏览器打开：http://localhost:8080

## 🎯 功能演示

### 基础操作

1. **发送消息**
   - 主题：test-topic
   - 消息内容：Hello Kafka!
   - 点击"发送消息"

2. **消费消息**
   - 主题：test-topic
   - 消费组：test-group
   - 点击"开始消费"

3. **观察结果**
   - 实时消息流显示生产和消费的消息
   - 统计信息实时更新
   - WebSocket 连接状态显示

### 高级功能

1. **多消费者测试**
   - 启动多个相同消费组的消费者
   - 观察消息负载均衡

2. **不同消费组测试**
   - 启动不同消费组的消费者
   - 观察消息广播效果

3. **消费者管理**
   - 查看活跃消费者列表
   - 停止指定消费者

## 🔧 配置说明

### 环境变量配置

复制 `.env.example` 为 `.env` 并修改配置：

```bash
# 服务器端口
SERVER_PORT=8080

# Kafka 地址
KAFKA_BROKERS=localhost:9092

# 消息历史数量
KAFKA_MAX_MESSAGE_HISTORY=1000

# 调试模式
DEBUG=false
```

### Kafka 配置

默认配置适合开发环境，生产环境建议调整：

- **生产者批处理大小**：100 条消息
- **消费者最小读取**：10KB
- **消费者最大读取**：10MB
- **提交间隔**：1 秒

## 🐛 故障排除

### 常见问题

1. **Docker 启动失败**
   ```bash
   # 检查 Docker 状态
   docker --version
   docker-compose --version
   
   # 重新启动
   docker-compose down
   docker-compose up -d
   ```

2. **端口被占用**
   ```bash
   # 检查端口占用
   netstat -ano | findstr :8080
   netstat -ano | findstr :9092
   
   # 修改配置文件中的端口
   ```

3. **Go 依赖下载失败**
   ```bash
   # 设置 Go 代理
   go env -w GOPROXY=https://goproxy.cn,direct
   go mod tidy
   ```

4. **WebSocket 连接失败**
   - 检查防火墙设置
   - 确认后端服务正常运行
   - 查看浏览器控制台错误

### 日志查看

1. **Kafka 日志**
   ```bash
   docker-compose logs kafka
   ```

2. **应用日志**
   - 后端日志直接在控制台显示
   - 设置 `DEBUG=true` 查看详细日志

3. **浏览器日志**
   - 按 F12 打开开发者工具
   - 查看 Console 和 Network 标签

## 📊 监控界面

### Kafka UI

访问 http://localhost:8081 查看：
- 主题列表和配置
- 分区和副本状态
- 消费者组信息
- 消息浏览

### 应用监控

访问 http://localhost:8080/health 查看：
- 服务健康状态
- 版本信息
- 运行时间

## 🔄 重启和清理

### 重启服务

```bash
# 重启 Kafka
docker-compose restart

# 重启应用
# Ctrl+C 停止后端，然后重新运行 start.bat
```

### 清理数据

```bash
# 清理 Kafka 数据
docker-compose down -v

# 清理 Docker 镜像（可选）
docker system prune -a
```

## 📝 开发说明

### 项目结构
```
kafka-demo/
├── backend/           # Go 后端
├── frontend/          # Vue 前端
├── docker-compose.yml # Kafka 环境
├── start-kafka.bat   # Kafka 启动脚本
├── start.bat         # 应用启动脚本
└── README.md         # 详细文档
```

### 技术栈
- **后端**：Go + Gin + WebSocket
- **前端**：Vue 3 + Element Plus
- **消息队列**：Apache Kafka
- **容器化**：Docker + Docker Compose

### 扩展开发

1. **添加新功能**
   - 修改 `handlers/kafka_handler.go`
   - 更新前端 `src/app.js`

2. **修改配置**
   - 更新 `config/config.go`
   - 修改环境变量

3. **自定义主题**
   - 在 Kafka UI 中创建主题
   - 或通过代码自动创建

## 🎓 学习资源

- [Apache Kafka 官方文档](https://kafka.apache.org/documentation/)
- [Go Kafka 客户端文档](https://github.com/segmentio/kafka-go)
- [Gin Web 框架](https://gin-gonic.com/)
- [Vue 3 官方文档](https://vuejs.org/)

## 💡 最佳实践

1. **生产环境部署**
   - 使用外部 Kafka 集群
   - 配置适当的副本和分区
   - 启用认证和加密

2. **性能优化**
   - 调整批处理大小
   - 优化消费者配置
   - 监控资源使用

3. **错误处理**
   - 实现重试机制
   - 添加死信队列
   - 完善日志记录
