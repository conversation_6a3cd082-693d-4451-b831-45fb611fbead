@echo off
echo ================================
echo Kafka Demo 启动脚本
echo ================================
echo.

echo 检查 Go 环境...
go version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到 Go 环境，请先安装 Go 1.21+
    pause
    exit /b 1
)

echo Go 环境检查通过
echo.

echo 进入后端目录...
cd /d "%~dp0backend"

echo 下载依赖包...
go mod tidy
if %errorlevel% neq 0 (
    echo 错误: 下载依赖包失败
    pause
    exit /b 1
)

echo 编译应用程序...
go build -o kafka-demo.exe .
if %errorlevel% neq 0 (
    echo 错误: 编译失败
    pause
    exit /b 1
)

echo.
echo 启动后端服务...
echo 服务地址: http://localhost:8080
echo 前端界面: http://localhost:8080
echo.
echo 按 Ctrl+C 停止服务
echo.

kafka-demo.exe

pause
