const { createApp } = Vue;
const { ElMessage, ElMessageBox } = ElementPlus;

createApp({
    data() {
        return {
            // WebSocket 连接
            ws: null,
            wsConnected: false,
            
            // 消息数据
            messages: [],
            
            // 生产者表单
            produceForm: {
                topic: 'test-topic',
                key: '',
                value: ''
            },
            producing: false,
            
            // 消费者表单
            consumeForm: {
                topic: 'test-topic',
                groupId: 'test-group'
            },
            starting: false,
            
            // 消费者列表
            consumers: [],

            // API 基础 URL
            apiBase: 'http://localhost:8080/api'
        };
    },
    
    computed: {
        // 反转消息顺序，最新的在上面
        reversedMessages() {
            return [...this.messages].reverse();
        },
        
        // 统计信息
        stats() {
            const total = this.messages.length;
            const produced = this.messages.filter(m => m.type === 'produced').length;
            const consumed = this.messages.filter(m => m.type === 'consumed').length;

            return {
                totalMessages: total,
                producedMessages: produced,
                consumedMessages: consumed
            };
        },

        // 活跃消费者数量
        activeConsumers() {
            return this.consumers.length;
        }
    },
    
    mounted() {
        this.initWebSocket();
        this.loadMessages();
        this.loadConsumers();

        // 定期刷新消费者状态
        setInterval(() => {
            this.loadConsumers();
        }, 5000);
    },
    
    beforeUnmount() {
        if (this.ws) {
            this.ws.close();
        }
    },
    
    methods: {
        // 初始化 WebSocket 连接
        initWebSocket() {
            const wsUrl = 'ws://localhost:8080/api/ws';
            this.ws = new WebSocket(wsUrl);
            
            this.ws.onopen = () => {
                this.wsConnected = true;
                console.log('WebSocket 连接已建立');
                ElMessage.success('WebSocket 连接成功');
            };
            
            this.ws.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    if (data.type === 'message') {
                        this.addMessage(data.data);
                    }
                } catch (error) {
                    console.error('解析 WebSocket 消息失败:', error);
                }
            };
            
            this.ws.onclose = () => {
                this.wsConnected = false;
                console.log('WebSocket 连接已关闭');
                ElMessage.warning('WebSocket 连接断开');
                
                // 尝试重连
                setTimeout(() => {
                    if (!this.wsConnected) {
                        this.initWebSocket();
                    }
                }, 3000);
            };
            
            this.ws.onerror = (error) => {
                console.error('WebSocket 错误:', error);
                ElMessage.error('WebSocket 连接错误');
            };
        },
        
        // 加载历史消息
        async loadMessages() {
            try {
                const response = await fetch(`${this.apiBase}/messages`);
                const result = await response.json();

                if (result.success && result.data) {
                    this.messages = result.data;
                }
            } catch (error) {
                console.error('加载消息失败:', error);
                ElMessage.error('加载历史消息失败');
            }
        },

        // 加载消费者状态
        async loadConsumers() {
            try {
                const response = await fetch(`${this.apiBase}/consumers`);
                const result = await response.json();

                if (result.success && result.data) {
                    this.consumers = result.data;
                }
            } catch (error) {
                console.error('加载消费者状态失败:', error);
            }
        },
        
        // 添加消息到列表
        addMessage(message) {
            // 检查消息是否已存在
            const exists = this.messages.some(m => m.id === message.id);
            if (!exists) {
                this.messages.push(message);
                
                // 限制消息数量
                if (this.messages.length > 1000) {
                    this.messages = this.messages.slice(-1000);
                }
            }
        },
        
        // 生产消息
        async produceMessage() {
            if (!this.produceForm.topic || !this.produceForm.value) {
                ElMessage.warning('请填写主题和消息内容');
                return;
            }
            
            this.producing = true;
            
            try {
                const response = await fetch(`${this.apiBase}/produce`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(this.produceForm)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    ElMessage.success('消息发送成功');
                    this.produceForm.value = ''; // 清空消息内容
                } else {
                    ElMessage.error('消息发送失败: ' + result.message);
                }
            } catch (error) {
                console.error('发送消息失败:', error);
                ElMessage.error('发送消息失败: ' + error.message);
            } finally {
                this.producing = false;
            }
        },
        
        // 开始消费
        async startConsumer() {
            if (!this.consumeForm.topic || !this.consumeForm.groupId) {
                ElMessage.warning('请填写主题和消费组 ID');
                return;
            }
            
            this.starting = true;
            
            try {
                const response = await fetch(`${this.apiBase}/consume`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(this.consumeForm)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    ElMessage.success('消费者启动成功');
                    this.loadConsumers(); // 刷新消费者列表
                } else {
                    ElMessage.error('启动消费者失败: ' + result.message);
                }
            } catch (error) {
                console.error('启动消费者失败:', error);
                ElMessage.error('启动消费者失败: ' + error.message);
            } finally {
                this.starting = false;
            }
        },

        // 停止消费者
        async stopConsumer(consumerKey) {
            try {
                const response = await fetch(`${this.apiBase}/stop-consumer`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ consumerKey })
                });

                const result = await response.json();

                if (result.success) {
                    ElMessage.success('消费者已停止');
                    this.loadConsumers(); // 刷新消费者列表
                } else {
                    ElMessage.error('停止消费者失败: ' + result.message);
                }
            } catch (error) {
                console.error('停止消费者失败:', error);
                ElMessage.error('停止消费者失败: ' + error.message);
            }
        },

        // 清空生产者表单
        clearProduceForm() {
            this.produceForm.key = '';
            this.produceForm.value = '';
        },
        
        // 清空消费者表单
        clearConsumeForm() {
            this.consumeForm.topic = 'test-topic';
            this.consumeForm.groupId = 'test-group';
        },
        
        // 清空消息
        clearMessages() {
            ElMessageBox.confirm('确定要清空所有消息吗？', '确认', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.messages = [];
                ElMessage.success('消息已清空');
            }).catch(() => {
                // 用户取消
            });
        },
        
        // 格式化时间
        formatTime(timestamp) {
            const date = new Date(timestamp);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        }
    }
}).use(ElementPlus).mount('#app');
