<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> 生产消费演示</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus@2.4.4/dist/index.full.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/element-plus@2.4.4/dist/index.css">
    <style>
        body {
            margin: 0;
            font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .control-panel {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .message-display {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            height: 500px;
            overflow-y: auto;
        }
        .message-item {
            border: 1px solid #e4e7ed;
            border-radius: 4px;
            padding: 12px;
            margin-bottom: 10px;
            background-color: #fafafa;
        }
        .message-item.produced {
            border-left: 4px solid #67c23a;
        }
        .message-item.consumed {
            border-left: 4px solid #409eff;
        }
        .message-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        .message-type {
            font-weight: bold;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
        }
        .message-type.produced {
            background-color: #f0f9ff;
            color: #67c23a;
        }
        .message-type.consumed {
            background-color: #ecf5ff;
            color: #409eff;
        }
        .message-content {
            font-family: 'Courier New', monospace;
            background-color: #f8f9fa;
            padding: 8px;
            border-radius: 4px;
            word-break: break-all;
        }
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-connected {
            background-color: #67c23a;
        }
        .status-disconnected {
            background-color: #f56c6c;
        }
        .stats {
            display: flex;
            justify-content: space-around;
            margin-bottom: 20px;
        }
        .stat-item {
            text-align: center;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            flex: 1;
            margin: 0 10px;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #409eff;
        }
        .stat-label {
            color: #909399;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <div class="header">
                <h1>Kafka 生产消费演示</h1>
                <p>
                    <span class="status-indicator" :class="wsConnected ? 'status-connected' : 'status-disconnected'"></span>
                    WebSocket 连接状态: {{ wsConnected ? '已连接' : '未连接' }}
                </p>
            </div>

            <!-- 统计信息 -->
            <div class="stats">
                <div class="stat-item">
                    <div class="stat-number">{{ stats.totalMessages }}</div>
                    <div class="stat-label">总消息数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ stats.producedMessages }}</div>
                    <div class="stat-label">已生产</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ stats.consumedMessages }}</div>
                    <div class="stat-label">已消费</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ activeConsumers }}</div>
                    <div class="stat-label">活跃消费者</div>
                </div>
            </div>

            <!-- 控制面板 -->
            <div class="control-panel">
                <el-row :gutter="20">
                    <!-- 生产者控制 -->
                    <el-col :span="12">
                        <h3>消息生产者</h3>
                        <el-form :model="produceForm" label-width="80px">
                            <el-form-item label="主题">
                                <el-input v-model="produceForm.topic" placeholder="输入 Kafka 主题名称"></el-input>
                            </el-form-item>
                            <el-form-item label="键">
                                <el-input v-model="produceForm.key" placeholder="消息键 (可选)"></el-input>
                            </el-form-item>
                            <el-form-item label="消息内容">
                                <el-input 
                                    v-model="produceForm.value" 
                                    type="textarea" 
                                    :rows="3"
                                    placeholder="输入要发送的消息内容">
                                </el-input>
                            </el-form-item>
                            <el-form-item>
                                <el-button type="primary" @click="produceMessage" :loading="producing">
                                    发送消息
                                </el-button>
                                <el-button @click="clearProduceForm">清空</el-button>
                            </el-form-item>
                        </el-form>
                    </el-col>

                    <!-- 消费者控制 -->
                    <el-col :span="12">
                        <h3>消息消费者</h3>
                        <el-form :model="consumeForm" label-width="80px">
                            <el-form-item label="主题">
                                <el-input v-model="consumeForm.topic" placeholder="输入 Kafka 主题名称"></el-input>
                            </el-form-item>
                            <el-form-item label="消费组">
                                <el-input v-model="consumeForm.groupId" placeholder="消费者组 ID"></el-input>
                            </el-form-item>
                            <el-form-item>
                                <el-button type="success" @click="startConsumer" :loading="starting">
                                    开始消费
                                </el-button>
                                <el-button @click="clearConsumeForm">清空</el-button>
                                <el-button type="warning" @click="clearMessages">清空消息</el-button>
                            </el-form-item>
                        </el-form>

                        <!-- 活跃消费者列表 -->
                        <div v-if="consumers.length > 0" style="margin-top: 20px;">
                            <h4>活跃消费者</h4>
                            <div v-for="consumer in consumers" :key="consumer.consumerKey"
                                 style="display: flex; justify-content: space-between; align-items: center;
                                        padding: 8px; margin: 5px 0; background: #f5f5f5; border-radius: 4px;">
                                <div>
                                    <strong>{{ consumer.topic }}</strong>
                                    <span style="color: #666;">| {{ consumer.groupId }}</span>
                                </div>
                                <el-button size="small" type="danger" @click="stopConsumer(consumer.consumerKey)">
                                    停止
                                </el-button>
                            </div>
                        </div>
                    </el-col>
                </el-row>
            </div>

            <!-- 消息显示区域 -->
            <div class="message-display">
                <h3>实时消息流</h3>
                <div v-if="messages.length === 0" style="text-align: center; color: #909399; padding: 50px;">
                    暂无消息，请发送或消费消息
                </div>
                <div v-for="message in reversedMessages" :key="message.id" 
                     :class="['message-item', message.type]">
                    <div class="message-header">
                        <div>
                            <span :class="['message-type', message.type]">
                                {{ message.type === 'produced' ? '生产' : '消费' }}
                            </span>
                            <strong>主题:</strong> {{ message.topic }}
                            <span v-if="message.key"> | <strong>键:</strong> {{ message.key }}</span>
                        </div>
                        <small>{{ formatTime(message.timestamp) }}</small>
                    </div>
                    <div class="message-content">
                        {{ message.value }}
                    </div>
                    <div v-if="message.partition !== undefined" style="margin-top: 5px; font-size: 12px; color: #909399;">
                        分区: {{ message.partition }} | 偏移量: {{ message.offset }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="src/app.js"></script>
</body>
</html>
