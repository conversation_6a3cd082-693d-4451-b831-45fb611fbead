<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> 生产消费演示 - 工作版本</title>
    <style>
        * { box-sizing: border-box; margin: 0; padding: 0; }
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .status {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status.connected { background-color: #28a745; }
        .status.disconnected { background-color: #dc3545; }
        .card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .controls {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }
        input, textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        input:focus, textarea:focus {
            outline: none;
            border-color: #667eea;
        }
        textarea {
            resize: vertical;
            min-height: 80px;
        }
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            margin-right: 10px;
            margin-bottom: 10px;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        button:active { transform: translateY(0); }
        button.success { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); }
        button.danger { background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%); }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }
        .messages {
            max-height: 500px;
            overflow-y: auto;
            border-radius: 10px;
            background: #f8f9fa;
            padding: 10px;
        }
        .message {
            background: white;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 10px;
            border-left: 5px solid #667eea;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .message.produced { border-left-color: #28a745; }
        .message.consumed { border-left-color: #007bff; }
        .message-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .message-type {
            background: #667eea;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        .message-type.produced { background: #28a745; }
        .message-type.consumed { background: #007bff; }
        .message-content {
            font-family: 'Courier New', monospace;
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            word-break: break-all;
        }
        .consumer-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 8px;
        }
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: bold;
        }
        .alert.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .alert.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        @media (max-width: 768px) {
            .controls { grid-template-columns: 1fr; }
            .stats { grid-template-columns: repeat(2, 1fr); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Kafka 生产消费演示</h1>
            <p>
                <span id="wsStatus" class="status disconnected"></span>
                <span id="statusText">正在连接服务器...</span>
            </p>
        </div>

        <div id="alertContainer"></div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="totalMessages">0</div>
                <div>总消息数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="producedMessages">0</div>
                <div>已生产</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="consumedMessages">0</div>
                <div>已消费</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="activeConsumers">0</div>
                <div>活跃消费者</div>
            </div>
        </div>

        <div class="controls">
            <div class="card">
                <h3>📤 消息生产者</h3>
                <div class="form-group">
                    <label>主题名称:</label>
                    <input type="text" id="produceTopic" value="test-topic" placeholder="Kafka 主题">
                </div>
                <div class="form-group">
                    <label>消息内容:</label>
                    <textarea id="produceMessage" placeholder="输入要发送的消息内容"></textarea>
                </div>
                <button id="sendBtn" onclick="sendMessage()">发送消息</button>
                <button onclick="clearProduceForm()">清空</button>
            </div>

            <div class="card">
                <h3>📥 消息消费者</h3>
                <div class="form-group">
                    <label>主题名称:</label>
                    <input type="text" id="consumeTopic" value="test-topic" placeholder="Kafka 主题">
                </div>
                <div class="form-group">
                    <label>消费组 ID:</label>
                    <input type="text" id="consumerGroup" value="test-group" placeholder="消费者组 ID">
                </div>
                <button id="startConsumerBtn" class="success" onclick="startConsumer()">开始消费</button>
                <button onclick="clearConsumeForm()">清空</button>
                <div id="consumerList"></div>
            </div>
        </div>

        <div class="card">
            <h3>💬 实时消息流</h3>
            <button onclick="loadMessages()">刷新</button>
            <button class="danger" onclick="clearMessages()">清空</button>
            <div id="messages" class="messages">
                <div style="text-align: center; padding: 50px; color: #666;">
                    暂无消息，请发送或消费消息
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let ws = null;
        let wsConnected = false;
        let messages = [];
        let consumers = [];
        const apiBase = '/api';

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，开始初始化...');
            checkServerHealth();
            initWebSocket();
            loadMessages();
            loadConsumers();
            
            // 定期刷新消费者状态
            setInterval(loadConsumers, 5000);
        });

        // 显示提示信息
        function showAlert(message, type = 'success') {
            console.log('Alert:', type, message);
            const container = document.getElementById('alertContainer');
            const alert = document.createElement('div');
            alert.className = 'alert ' + type;
            alert.textContent = message;
            
            container.appendChild(alert);
            
            // 3秒后自动移除
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.parentNode.removeChild(alert);
                }
            }, 3000);
        }

        // 更新状态显示
        function updateStatus(connected) {
            const statusEl = document.getElementById('wsStatus');
            const textEl = document.getElementById('statusText');
            
            if (connected) {
                statusEl.className = 'status connected';
                textEl.textContent = 'WebSocket 已连接';
            } else {
                statusEl.className = 'status disconnected';
                textEl.textContent = 'WebSocket 未连接';
            }
        }

        // 检查服务器健康状态
        async function checkServerHealth() {
            try {
                console.log('检查服务器健康状态...');
                const response = await fetch('/health');
                const result = await response.json();
                
                if (result.status === 'ok') {
                    showAlert('服务器连接正常', 'success');
                    console.log('服务器健康检查通过');
                } else {
                    showAlert('服务器状态异常', 'error');
                }
            } catch (error) {
                showAlert('无法连接到服务器: ' + error.message, 'error');
                console.error('Health check failed:', error);
            }
        }

        // 初始化 WebSocket
        function initWebSocket() {
            try {
                console.log('初始化 WebSocket 连接...');
                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                const wsUrl = protocol + '//' + window.location.host + '/api/ws';
                console.log('WebSocket URL:', wsUrl);
                
                ws = new WebSocket(wsUrl);
                
                ws.onopen = function() {
                    wsConnected = true;
                    updateStatus(true);
                    showAlert('WebSocket 连接成功', 'success');
                    console.log('WebSocket connected');
                };
                
                ws.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        console.log('收到 WebSocket 消息:', data);
                        if (data.type === 'message') {
                            addMessage(data.data);
                        }
                    } catch (error) {
                        console.error('Parse WebSocket message failed:', error);
                    }
                };
                
                ws.onclose = function() {
                    wsConnected = false;
                    updateStatus(false);
                    console.log('WebSocket disconnected');
                    
                    // 尝试重连
                    setTimeout(initWebSocket, 3000);
                };
                
                ws.onerror = function(error) {
                    updateStatus(false);
                    console.error('WebSocket error:', error);
                };
            } catch (error) {
                showAlert('WebSocket 初始化失败: ' + error.message, 'error');
                console.error('WebSocket init failed:', error);
            }
        }

        // 发送消息
        async function sendMessage() {
            const topic = document.getElementById('produceTopic').value;
            const message = document.getElementById('produceMessage').value;
            
            if (!topic || !message) {
                showAlert('请填写主题和消息内容', 'warning');
                return;
            }
            
            const btn = document.getElementById('sendBtn');
            btn.disabled = true;
            btn.textContent = '发送中...';
            
            try {
                console.log('发送消息:', { topic, message });
                const response = await fetch(apiBase + '/produce', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ topic: topic, key: '', value: message })
                });
                
                const result = await response.json();
                console.log('发送结果:', result);
                
                if (result.success) {
                    showAlert('消息发送成功', 'success');
                    document.getElementById('produceMessage').value = '';
                } else {
                    showAlert('发送失败: ' + result.message, 'error');
                }
            } catch (error) {
                showAlert('发送错误: ' + error.message, 'error');
                console.error('Send message failed:', error);
            } finally {
                btn.disabled = false;
                btn.textContent = '发送消息';
            }
        }

        // 开始消费
        async function startConsumer() {
            const topic = document.getElementById('consumeTopic').value;
            const groupId = document.getElementById('consumerGroup').value;
            
            if (!topic || !groupId) {
                showAlert('请填写主题和消费组 ID', 'warning');
                return;
            }
            
            const btn = document.getElementById('startConsumerBtn');
            btn.disabled = true;
            btn.textContent = '启动中...';
            
            try {
                console.log('启动消费者:', { topic, groupId });
                const response = await fetch(apiBase + '/consume', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ topic: topic, groupId: groupId })
                });
                
                const result = await response.json();
                console.log('启动结果:', result);
                
                if (result.success) {
                    showAlert('消费者启动成功', 'success');
                    loadConsumers();
                } else {
                    showAlert('启动失败: ' + result.message, 'error');
                }
            } catch (error) {
                showAlert('启动错误: ' + error.message, 'error');
                console.error('Start consumer failed:', error);
            } finally {
                btn.disabled = false;
                btn.textContent = '开始消费';
            }
        }

        // 停止消费者
        async function stopConsumer(consumerKey) {
            try {
                console.log('停止消费者:', consumerKey);
                const response = await fetch(apiBase + '/stop-consumer', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ consumerKey: consumerKey })
                });
                
                const result = await response.json();
                console.log('停止结果:', result);
                
                if (result.success) {
                    showAlert('消费者已停止', 'success');
                    loadConsumers();
                } else {
                    showAlert('停止失败: ' + result.message, 'error');
                }
            } catch (error) {
                showAlert('停止错误: ' + error.message, 'error');
                console.error('Stop consumer failed:', error);
            }
        }

        // 加载消费者列表
        async function loadConsumers() {
            try {
                const response = await fetch(apiBase + '/consumers');
                const result = await response.json();
                
                if (result.success && result.data) {
                    consumers = result.data;
                    updateConsumerList();
                    updateStats();
                    console.log('消费者列表:', consumers);
                }
            } catch (error) {
                console.error('Load consumers failed:', error);
            }
        }

        // 更新消费者列表显示
        function updateConsumerList() {
            const container = document.getElementById('consumerList');
            
            if (consumers.length === 0) {
                container.innerHTML = '';
                return;
            }
            
            container.innerHTML = '<h4>活跃消费者:</h4>';
            
            consumers.forEach(consumer => {
                const item = document.createElement('div');
                item.className = 'consumer-item';
                item.innerHTML = `
                    <div>
                        <strong>${consumer.topic}</strong> | ${consumer.groupId}
                    </div>
                    <button class="danger" onclick="stopConsumer('${consumer.consumerKey}')">停止</button>
                `;
                container.appendChild(item);
            });
        }

        // 加载历史消息
        async function loadMessages() {
            try {
                console.log('加载历史消息...');
                const response = await fetch(apiBase + '/messages');
                const result = await response.json();
                
                if (result.success && result.data) {
                    messages = result.data;
                    updateMessageDisplay();
                    updateStats();
                    console.log('历史消息:', messages.length, '条');
                }
            } catch (error) {
                console.error('Load messages failed:', error);
            }
        }

        // 添加消息
        function addMessage(message) {
            const exists = messages.some(m => m.id === message.id);
            if (!exists) {
                messages.push(message);
                console.log('新消息:', message);
                
                // 限制消息数量
                if (messages.length > 1000) {
                    messages = messages.slice(-1000);
                }
                
                updateMessageDisplay();
                updateStats();
            }
        }

        // 更新消息显示
        function updateMessageDisplay() {
            const container = document.getElementById('messages');
            
            if (messages.length === 0) {
                container.innerHTML = `
                    <div style="text-align: center; padding: 50px; color: #666;">
                        暂无消息，请发送或消费消息
                    </div>
                `;
                return;
            }
            
            // 按时间倒序显示
            const sortedMessages = [...messages].reverse();
            
            container.innerHTML = '';
            
            sortedMessages.forEach(message => {
                const messageEl = document.createElement('div');
                messageEl.className = `message ${message.type}`;
                
                const time = new Date(message.timestamp).toLocaleString('zh-CN');
                
                messageEl.innerHTML = `
                    <div class="message-header">
                        <div>
                            <span class="message-type ${message.type}">
                                ${message.type === 'produced' ? '生产' : '消费'}
                            </span>
                            <strong>主题:</strong> ${message.topic}
                            ${message.key ? ` | <strong>键:</strong> ${message.key}` : ''}
                        </div>
                        <small>${time}</small>
                    </div>
                    <div class="message-content">${message.value}</div>
                    ${message.partition !== undefined ? 
                        `<div style="margin-top: 5px; font-size: 12px; color: #666;">
                            分区: ${message.partition} | 偏移量: ${message.offset}
                        </div>` : ''
                    }
                `;
                
                container.appendChild(messageEl);
            });
        }

        // 更新统计信息
        function updateStats() {
            const total = messages.length;
            const produced = messages.filter(m => m.type === 'produced').length;
            const consumed = messages.filter(m => m.type === 'consumed').length;
            
            document.getElementById('totalMessages').textContent = total;
            document.getElementById('producedMessages').textContent = produced;
            document.getElementById('consumedMessages').textContent = consumed;
            document.getElementById('activeConsumers').textContent = consumers.length;
        }

        // 清空表单
        function clearProduceForm() {
            document.getElementById('produceMessage').value = '';
        }

        function clearConsumeForm() {
            document.getElementById('consumeTopic').value = 'test-topic';
            document.getElementById('consumerGroup').value = 'test-group';
        }

        // 清空消息
        function clearMessages() {
            if (confirm('确定要清空所有消息吗？')) {
                messages = [];
                updateMessageDisplay();
                updateStats();
                showAlert('消息已清空', 'success');
            }
        }
    </script>
</body>
</html>
