<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> 生产消费演示 - 简化版</title>
    <style>
        * { box-sizing: border-box; }
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status.connected { background-color: #28a745; }
        .status.disconnected { background-color: #dc3545; }
        .panel {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        .col {
            flex: 1;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        textarea {
            resize: vertical;
            min-height: 80px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover { background-color: #0056b3; }
        button.success { background-color: #28a745; }
        button.success:hover { background-color: #1e7e34; }
        button.danger { background-color: #dc3545; }
        button.danger:hover { background-color: #c82333; }
        button.warning { background-color: #ffc107; color: #212529; }
        button.warning:hover { background-color: #e0a800; }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .stats {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
        }
        .stat-card {
            flex: 1;
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .stat-number {
            font-size: 28px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }
        .stat-label {
            color: #666;
            font-size: 14px;
        }
        .messages {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f8f9fa;
        }
        .message {
            padding: 12px;
            border-bottom: 1px solid #eee;
            background: white;
            margin: 2px;
            border-radius: 4px;
        }
        .message:last-child { border-bottom: none; }
        .message.produced {
            border-left: 4px solid #28a745;
        }
        .message.consumed {
            border-left: 4px solid #007bff;
        }
        .message-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        .message-type {
            background: #007bff;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        .message-type.produced { background: #28a745; }
        .message-content {
            font-family: 'Courier New', monospace;
            background: #f8f9fa;
            padding: 8px;
            border-radius: 4px;
            word-break: break-all;
        }
        .consumer-list {
            margin-top: 15px;
        }
        .consumer-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            margin-bottom: 8px;
        }
        .alert {
            padding: 12px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        .alert.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .alert.warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <h1>Kafka 生产消费演示</h1>
            <p>
                <span id="wsStatus" class="status disconnected"></span>
                <span id="statusText">正在连接...</span>
            </p>
        </div>

        <!-- 状态提示 -->
        <div id="alertContainer"></div>

        <!-- 统计信息 -->
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="totalMessages">0</div>
                <div class="stat-label">总消息数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="producedMessages">0</div>
                <div class="stat-label">已生产</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="consumedMessages">0</div>
                <div class="stat-label">已消费</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="activeConsumers">0</div>
                <div class="stat-label">活跃消费者</div>
            </div>
        </div>

        <!-- 控制面板 -->
        <div class="panel">
            <div class="row">
                <!-- 生产者 -->
                <div class="col">
                    <h3>消息生产者</h3>
                    <div class="form-group">
                        <label>主题名称:</label>
                        <input type="text" id="produceTopic" value="test-topic" placeholder="Kafka 主题">
                    </div>
                    <div class="form-group">
                        <label>消息键 (可选):</label>
                        <input type="text" id="produceKey" placeholder="消息键">
                    </div>
                    <div class="form-group">
                        <label>消息内容:</label>
                        <textarea id="produceMessage" placeholder="输入要发送的消息内容"></textarea>
                    </div>
                    <button id="sendBtn" onclick="sendMessage()">发送消息</button>
                    <button onclick="clearProduceForm()">清空表单</button>
                </div>

                <!-- 消费者 -->
                <div class="col">
                    <h3>消息消费者</h3>
                    <div class="form-group">
                        <label>主题名称:</label>
                        <input type="text" id="consumeTopic" value="test-topic" placeholder="Kafka 主题">
                    </div>
                    <div class="form-group">
                        <label>消费组 ID:</label>
                        <input type="text" id="consumerGroup" value="test-group" placeholder="消费者组 ID">
                    </div>
                    <button id="startConsumerBtn" class="success" onclick="startConsumer()">开始消费</button>
                    <button onclick="clearConsumeForm()">清空表单</button>
                    <button class="warning" onclick="clearMessages()">清空消息</button>

                    <!-- 活跃消费者列表 -->
                    <div id="consumerList" class="consumer-list"></div>
                </div>
            </div>
        </div>

        <!-- 消息显示 -->
        <div class="panel">
            <h3>实时消息流</h3>
            <button onclick="loadMessages()">刷新消息</button>
            <button class="danger" onclick="clearMessages()">清空显示</button>
            <div id="messages" class="messages">
                <div style="text-align: center; padding: 50px; color: #666;">
                    暂无消息，请发送或消费消息
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let ws = null;
        let wsConnected = false;
        let messages = [];
        let consumers = [];
        const apiBase = 'http://localhost:8080/api';

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            checkServerHealth();
            initWebSocket();
            loadMessages();
            loadConsumers();

            // 定期刷新消费者状态
            setInterval(loadConsumers, 5000);
        });

        // 检查服务器健康状态
        async function checkServerHealth() {
            try {
                const response = await fetch('http://localhost:8080/health');
                const result = await response.json();

                if (result.status === 'ok') {
                    showAlert('服务器连接正常', 'success');
                } else {
                    showAlert('服务器状态异常', 'error');
                }
            } catch (error) {
                showAlert('无法连接到服务器: ' + error.message, 'error');
                console.error('Health check failed:', error);
            }
        }

        // 显示提示信息
        function showAlert(message, type = 'success') {
            const container = document.getElementById('alertContainer');
            const alert = document.createElement('div');
            alert.className = `alert ${type}`;
            alert.textContent = message;

            container.appendChild(alert);

            // 3秒后自动移除
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.parentNode.removeChild(alert);
                }
            }, 3000);
        }

        // 更新状态显示
        function updateStatus(connected) {
            const statusEl = document.getElementById('wsStatus');
            const textEl = document.getElementById('statusText');

            if (connected) {
                statusEl.className = 'status connected';
                textEl.textContent = 'WebSocket 已连接';
            } else {
                statusEl.className = 'status disconnected';
                textEl.textContent = 'WebSocket 未连接';
            }
        }

        // 初始化 WebSocket
        function initWebSocket() {
            try {
                ws = new WebSocket('ws://localhost:8080/api/ws');

                ws.onopen = function() {
                    wsConnected = true;
                    updateStatus(true);
                    showAlert('WebSocket 连接成功', 'success');
                    console.log('WebSocket connected');
                };

                ws.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        if (data.type === 'message') {
                            addMessage(data.data);
                        }
                    } catch (error) {
                        console.error('Parse WebSocket message failed:', error);
                    }
                };

                ws.onclose = function() {
                    wsConnected = false;
                    updateStatus(false);
                    showAlert('WebSocket 连接断开', 'warning');
                    console.log('WebSocket disconnected');

                    // 尝试重连
                    setTimeout(initWebSocket, 3000);
                };

                ws.onerror = function(error) {
                    updateStatus(false);
                    showAlert('WebSocket 连接错误', 'error');
                    console.error('WebSocket error:', error);
                };
            } catch (error) {
                showAlert('WebSocket 初始化失败: ' + error.message, 'error');
                console.error('WebSocket init failed:', error);
            }
        }

        // 发送消息
        async function sendMessage() {
            const topic = document.getElementById('produceTopic').value;
            const key = document.getElementById('produceKey').value;
            const message = document.getElementById('produceMessage').value;

            if (!topic || !message) {
                showAlert('请填写主题和消息内容', 'warning');
                return;
            }

            const btn = document.getElementById('sendBtn');
            btn.disabled = true;
            btn.textContent = '发送中...';

            try {
                const response = await fetch(`${apiBase}/produce`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        topic: topic,
                        key: key,
                        value: message
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showAlert('消息发送成功', 'success');
                    document.getElementById('produceMessage').value = '';
                } else {
                    showAlert('消息发送失败: ' + result.message, 'error');
                }
            } catch (error) {
                showAlert('发送消息时出错: ' + error.message, 'error');
                console.error('Send message failed:', error);
            } finally {
                btn.disabled = false;
                btn.textContent = '发送消息';
            }
        }

        // 开始消费
        async function startConsumer() {
            const topic = document.getElementById('consumeTopic').value;
            const groupId = document.getElementById('consumerGroup').value;

            if (!topic || !groupId) {
                showAlert('请填写主题和消费组 ID', 'warning');
                return;
            }

            const btn = document.getElementById('startConsumerBtn');
            btn.disabled = true;
            btn.textContent = '启动中...';

            try {
                const response = await fetch(`${apiBase}/consume`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        topic: topic,
                        groupId: groupId
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showAlert('消费者启动成功', 'success');
                    loadConsumers();
                } else {
                    showAlert('启动消费者失败: ' + result.message, 'error');
                }
            } catch (error) {
                showAlert('启动消费者时出错: ' + error.message, 'error');
                console.error('Start consumer failed:', error);
            } finally {
                btn.disabled = false;
                btn.textContent = '开始消费';
            }
        }

        // 停止消费者
        async function stopConsumer(consumerKey) {
            try {
                const response = await fetch(`${apiBase}/stop-consumer`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        consumerKey: consumerKey
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showAlert('消费者已停止', 'success');
                    loadConsumers();
                } else {
                    showAlert('停止消费者失败: ' + result.message, 'error');
                }
            } catch (error) {
                showAlert('停止消费者时出错: ' + error.message, 'error');
                console.error('Stop consumer failed:', error);
            }
        }

        // 加载消费者列表
        async function loadConsumers() {
            try {
                const response = await fetch(`${apiBase}/consumers`);
                const result = await response.json();

                if (result.success && result.data) {
                    consumers = result.data;
                    updateConsumerList();
                    updateStats();
                }
            } catch (error) {
                console.error('Load consumers failed:', error);
            }
        }

        // 更新消费者列表显示
        function updateConsumerList() {
            const container = document.getElementById('consumerList');

            if (consumers.length === 0) {
                container.innerHTML = '';
                return;
            }

            container.innerHTML = '<h4>活跃消费者:</h4>';

            consumers.forEach(consumer => {
                const item = document.createElement('div');
                item.className = 'consumer-item';
                item.innerHTML = `
                    <div>
                        <strong>${consumer.topic}</strong> | ${consumer.groupId}
                    </div>
                    <button class="danger" onclick="stopConsumer('${consumer.consumerKey}')">停止</button>
                `;
                container.appendChild(item);
            });
        }

        // 加载历史消息
        async function loadMessages() {
            try {
                const response = await fetch(`${apiBase}/messages`);
                const result = await response.json();

                if (result.success && result.data) {
                    messages = result.data;
                    updateMessageDisplay();
                    updateStats();
                }
            } catch (error) {
                console.error('Load messages failed:', error);
            }
        }

        // 添加消息
        function addMessage(message) {
            const exists = messages.some(m => m.id === message.id);
            if (!exists) {
                messages.push(message);

                // 限制消息数量
                if (messages.length > 1000) {
                    messages = messages.slice(-1000);
                }

                updateMessageDisplay();
                updateStats();
            }
        }

        // 更新消息显示
        function updateMessageDisplay() {
            const container = document.getElementById('messages');

            if (messages.length === 0) {
                container.innerHTML = `
                    <div style="text-align: center; padding: 50px; color: #666;">
                        暂无消息，请发送或消费消息
                    </div>
                `;
                return;
            }

            // 按时间倒序显示
            const sortedMessages = [...messages].reverse();

            container.innerHTML = '';

            sortedMessages.forEach(message => {
                const messageEl = document.createElement('div');
                messageEl.className = `message ${message.type}`;

                const time = new Date(message.timestamp).toLocaleString('zh-CN');

                messageEl.innerHTML = `
                    <div class="message-header">
                        <div>
                            <span class="message-type ${message.type}">
                                ${message.type === 'produced' ? '生产' : '消费'}
                            </span>
                            <strong>主题:</strong> ${message.topic}
                            ${message.key ? ` | <strong>键:</strong> ${message.key}` : ''}
                        </div>
                        <small>${time}</small>
                    </div>
                    <div class="message-content">${message.value}</div>
                    ${message.partition !== undefined ?
                        `<div style="margin-top: 5px; font-size: 12px; color: #666;">
                            分区: ${message.partition} | 偏移量: ${message.offset}
                        </div>` : ''
                    }
                `;

                container.appendChild(messageEl);
            });
        }

        // 更新统计信息
        function updateStats() {
            const total = messages.length;
            const produced = messages.filter(m => m.type === 'produced').length;
            const consumed = messages.filter(m => m.type === 'consumed').length;

            document.getElementById('totalMessages').textContent = total;
            document.getElementById('producedMessages').textContent = produced;
            document.getElementById('consumedMessages').textContent = consumed;
            document.getElementById('activeConsumers').textContent = consumers.length;
        }

        // 清空表单
        function clearProduceForm() {
            document.getElementById('produceKey').value = '';
            document.getElementById('produceMessage').value = '';
        }

        function clearConsumeForm() {
            document.getElementById('consumeTopic').value = 'test-topic';
            document.getElementById('consumerGroup').value = 'test-group';
        }

        // 清空消息
        function clearMessages() {
            if (confirm('确定要清空所有消息吗？')) {
                messages = [];
                updateMessageDisplay();
                updateStats();
                showAlert('消息已清空', 'success');
            }
        }
    </script>
</body>
</html>
