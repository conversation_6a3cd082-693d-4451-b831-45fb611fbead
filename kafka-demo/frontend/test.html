<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> - 测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .warning { background-color: #fff3cd; color: #856404; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        input, textarea {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .form-group {
            margin: 15px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        #messages {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
            background-color: #f8f9fa;
        }
        .message {
            padding: 8px;
            margin: 5px 0;
            border-left: 4px solid #007bff;
            background-color: white;
        }
        .message.produced { border-left-color: #28a745; }
        .message.consumed { border-left-color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Kafka 生产消费演示 - 测试页面</h1>
        
        <div id="status" class="status warning">
            正在连接服务器...
        </div>

        <div class="form-group">
            <h3>消息生产者</h3>
            <label>主题:</label>
            <input type="text" id="topic" value="test-topic" placeholder="Kafka 主题">
            
            <label>消息内容:</label>
            <textarea id="message" placeholder="输入消息内容" rows="3"></textarea>
            
            <button onclick="sendMessage()">发送消息</button>
        </div>

        <div class="form-group">
            <h3>消息消费者</h3>
            <label>消费组:</label>
            <input type="text" id="groupId" value="test-group" placeholder="消费者组 ID">
            
            <button onclick="startConsumer()">开始消费</button>
            <button onclick="stopAllConsumers()">停止所有消费者</button>
        </div>

        <div class="form-group">
            <h3>实时消息</h3>
            <button onclick="clearMessages()">清空消息</button>
            <div id="messages"></div>
        </div>
    </div>

    <script>
        let ws = null;
        let wsConnected = false;
        const apiBase = 'http://localhost:8080/api';
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            checkServerHealth();
            initWebSocket();
            loadMessages();
        });

        // 检查服务器健康状态
        async function checkServerHealth() {
            try {
                const response = await fetch('http://localhost:8080/health');
                const result = await response.json();
                
                if (result.status === 'ok') {
                    updateStatus('服务器连接正常', 'success');
                } else {
                    updateStatus('服务器状态异常', 'error');
                }
            } catch (error) {
                updateStatus('无法连接到服务器: ' + error.message, 'error');
                console.error('Health check failed:', error);
            }
        }

        // 更新状态显示
        function updateStatus(message, type) {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = 'status ' + type;
        }

        // 初始化 WebSocket
        function initWebSocket() {
            try {
                ws = new WebSocket('ws://localhost:8080/api/ws');
                
                ws.onopen = function() {
                    wsConnected = true;
                    updateStatus('WebSocket 连接成功', 'success');
                    console.log('WebSocket connected');
                };
                
                ws.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        if (data.type === 'message') {
                            addMessageToDisplay(data.data);
                        }
                    } catch (error) {
                        console.error('Parse WebSocket message failed:', error);
                    }
                };
                
                ws.onclose = function() {
                    wsConnected = false;
                    updateStatus('WebSocket 连接断开', 'warning');
                    console.log('WebSocket disconnected');
                    
                    // 尝试重连
                    setTimeout(initWebSocket, 3000);
                };
                
                ws.onerror = function(error) {
                    updateStatus('WebSocket 连接错误', 'error');
                    console.error('WebSocket error:', error);
                };
            } catch (error) {
                updateStatus('WebSocket 初始化失败: ' + error.message, 'error');
                console.error('WebSocket init failed:', error);
            }
        }

        // 发送消息
        async function sendMessage() {
            const topic = document.getElementById('topic').value;
            const message = document.getElementById('message').value;
            
            if (!topic || !message) {
                alert('请填写主题和消息内容');
                return;
            }
            
            try {
                const response = await fetch(`${apiBase}/produce`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        topic: topic,
                        key: '',
                        value: message
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    updateStatus('消息发送成功', 'success');
                    document.getElementById('message').value = '';
                } else {
                    updateStatus('消息发送失败: ' + result.message, 'error');
                }
            } catch (error) {
                updateStatus('发送消息时出错: ' + error.message, 'error');
                console.error('Send message failed:', error);
            }
        }

        // 开始消费
        async function startConsumer() {
            const topic = document.getElementById('topic').value;
            const groupId = document.getElementById('groupId').value;
            
            if (!topic || !groupId) {
                alert('请填写主题和消费组 ID');
                return;
            }
            
            try {
                const response = await fetch(`${apiBase}/consume`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        topic: topic,
                        groupId: groupId
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    updateStatus('消费者启动成功', 'success');
                } else {
                    updateStatus('启动消费者失败: ' + result.message, 'error');
                }
            } catch (error) {
                updateStatus('启动消费者时出错: ' + error.message, 'error');
                console.error('Start consumer failed:', error);
            }
        }

        // 停止所有消费者
        async function stopAllConsumers() {
            try {
                const response = await fetch(`${apiBase}/consumers`);
                const result = await response.json();
                
                if (result.success && result.data) {
                    for (const consumer of result.data) {
                        await fetch(`${apiBase}/stop-consumer`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                consumerKey: consumer.consumerKey
                            })
                        });
                    }
                    updateStatus('所有消费者已停止', 'success');
                }
            } catch (error) {
                updateStatus('停止消费者时出错: ' + error.message, 'error');
                console.error('Stop consumers failed:', error);
            }
        }

        // 加载历史消息
        async function loadMessages() {
            try {
                const response = await fetch(`${apiBase}/messages`);
                const result = await response.json();
                
                if (result.success && result.data) {
                    const messagesEl = document.getElementById('messages');
                    messagesEl.innerHTML = '';
                    
                    result.data.reverse().forEach(msg => {
                        addMessageToDisplay(msg);
                    });
                }
            } catch (error) {
                console.error('Load messages failed:', error);
            }
        }

        // 添加消息到显示区域
        function addMessageToDisplay(message) {
            const messagesEl = document.getElementById('messages');
            const messageEl = document.createElement('div');
            messageEl.className = 'message ' + message.type;
            
            const time = new Date(message.timestamp).toLocaleString('zh-CN');
            messageEl.innerHTML = `
                <strong>[${message.type === 'produced' ? '生产' : '消费'}]</strong> 
                ${message.topic} - ${message.value}
                <br><small>${time}</small>
            `;
            
            messagesEl.insertBefore(messageEl, messagesEl.firstChild);
            
            // 限制显示的消息数量
            while (messagesEl.children.length > 50) {
                messagesEl.removeChild(messagesEl.lastChild);
            }
        }

        // 清空消息
        function clearMessages() {
            if (confirm('确定要清空所有消息吗？')) {
                document.getElementById('messages').innerHTML = '';
                updateStatus('消息已清空', 'success');
            }
        }
    </script>
</body>
</html>
