@echo off
echo ================================
echo 前端测试脚本
echo ================================
echo.

echo 检查前端文件...
if not exist "frontend\index.html" (
    echo 错误: 找不到 frontend\index.html
    pause
    exit /b 1
)

if not exist "frontend\src\app.js" (
    echo 错误: 找不到 frontend\src\app.js
    pause
    exit /b 1
)

if not exist "frontend\test.html" (
    echo 错误: 找不到 frontend\test.html
    pause
    exit /b 1
)

echo 前端文件检查通过
echo.

echo 启动后端服务进行测试...
cd /d "%~dp0backend"

echo 编译应用程序...
go build -o kafka-demo.exe .
if %errorlevel% neq 0 (
    echo 错误: 编译失败
    pause
    exit /b 1
)

echo.
echo 启动测试服务器...
echo.
echo 测试页面:
echo - 主页面: http://localhost:8080
echo - 测试页面: http://localhost:8080/test
echo - 健康检查: http://localhost:8080/health
echo - API 文档: http://localhost:8080/api
echo.
echo 按 Ctrl+C 停止服务
echo.

kafka-demo.exe

pause
