package main

import (
	"fmt"
	"sync"
	"time"
)

// 使用channel进行goroutine间通信
func producer(ch chan<- int, wg *sync.WaitGroup) {
	defer wg.Done()
	for i := 0; i < 5; i++ {
		ch <- i
		fmt.Printf("生产者生产: %d\n", i)
		time.Sleep(time.Millisecond * 100)
	}
	close(ch)
}

func consumer(ch <-chan int, wg *sync.WaitGroup) {
	defer wg.Done()
	for num := range ch {
		fmt.Printf("消费者消费: %d\n", num)
		time.Sleep(time.Millisecond * 200)
	}
}

// 使用互斥锁保护共享资源
type SafeCounter struct {
	mu    sync.Mutex
	count int
}

func (c *SafeCounter) Increment() {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.count++
}

func (c *SafeCounter) GetCount() int {
	c.mu.Lock()
	defer c.mu.Unlock()
	return c.count
}

func main() {
	fmt.Println("1. 演示生产者-消费者模式:")
	var wg sync.WaitGroup
	ch := make(chan int)

	wg.Add(2)
	go producer(ch, &wg)
	go consumer(ch, &wg)
	wg.Wait()

	fmt.Println("\n2. 演示并发安全的计数器:")
	counter := SafeCounter{}
	for i := 0; i < 5; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			counter.Increment()
			fmt.Printf("当前计数: %d\n", counter.GetCount())
		}()
	}
	wg.Wait()

	fmt.Println("\n3. 演示select语句:")
	ch1 := make(chan string)
	ch2 := make(chan string)
	timeout := time.After(2 * time.Second)

	go func() {
		time.Sleep(time.Second)
		ch1 <- "来自通道1的消息"
	}()

	go func() {
		time.Sleep(time.Millisecond * 500)
		ch2 <- "来自通道2的消息"
	}()

	for i := 0; i < 2; i++ {
		select {
		case msg1 := <-ch1:
			fmt.Println(msg1)
		case msg2 := <-ch2:
			fmt.Println(msg2)
		case <-timeout:
			fmt.Println("操作超时")
			return
		}
	}
}
