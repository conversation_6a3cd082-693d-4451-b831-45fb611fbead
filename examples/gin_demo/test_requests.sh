#!/bin/bash

echo "=== 测试API请求，观察日志输出 ==="
echo

echo "1. 获取所有todos"
curl -X GET http://localhost:8080/api/todos
echo -e "\n"

echo "2. 获取单个todo (ID: 1)"
curl -X GET http://localhost:8080/api/todos/1
echo -e "\n"

echo "3. 创建新todo"
curl -X POST http://localhost:8080/api/todos \
  -H "Content-Type: application/json" \
  -d '{"id":"3","title":"测试新Todo","status":false}'
echo -e "\n"

echo "4. 更新todo (ID: 1)"
curl -X PUT http://localhost:8080/api/todos/1 \
  -H "Content-Type: application/json" \
  -d '{"id":"1","title":"学习Go语言","status":true}'
echo -e "\n"

echo "5. 删除todo (ID: 2)"
curl -X DELETE http://localhost:8080/api/todos/2
echo -e "\n"

echo "6. 尝试获取不存在的todo (ID: 999)"
curl -X GET http://localhost:8080/api/todos/999
echo -e "\n"

echo "=== 测试完成 ==="
