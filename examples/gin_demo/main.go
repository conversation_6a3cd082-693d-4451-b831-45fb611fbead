package main

import (
	"bytes"
	"fmt"
	"io"
	"log"
	"math/rand"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

// 定义一个简单的Todo结构体
type Todo struct {
	ID     string `json:"id"`
	Title  string `json:"title"`
	Status bool   `json:"status"`
}

// 内存中存储todos
var todos = []Todo{
	{ID: "1", Title: "学习Go语言", Status: false},
	{ID: "2", Title: "学习Gin框架", Status: false},
}

// 生成请求ID
func generateRequestID() string {
	return fmt.Sprintf("REQ-%d-%04d", time.Now().Unix(), rand.Intn(10000))
}

func main() {
	// 创建Gin引擎
	r := gin.Default()

	// 定义中间件
	r.Use(LoggerMiddleware())

	// 路由组
	api := r.Group("/api")
	{
		// GET /api/todos - 获取所有todos
		api.GET("/todos", func(c *gin.Context) {
			requestID, _ := c.Get("requestID")
			log.Printf("[%s] 获取所有todos，当前数量: %d", requestID, len(todos))
			c.JSON(http.StatusOK, gin.H{
				"todos": todos,
			})
		})

		// GET /api/todos/:id - 获取单个todo
		api.GET("/todos/:id", func(c *gin.Context) {
			requestID, _ := c.Get("requestID")
			id := c.Param("id")
			log.Printf("[%s] 查找Todo，ID: %s", requestID, id)
			for _, todo := range todos {
				if todo.ID == id {
					log.Printf("[%s] 找到Todo: %+v", requestID, todo)
					c.JSON(http.StatusOK, todo)
					return
				}
			}
			log.Printf("[%s] Todo不存在，ID: %s", requestID, id)
			c.JSON(http.StatusNotFound, gin.H{"message": "Todo不存在"})
		})

		// POST /api/todos - 创建新todo
		api.POST("/todos", func(c *gin.Context) {
			requestID, _ := c.Get("requestID")
			var newTodo Todo
			if err := c.BindJSON(&newTodo); err != nil {
				log.Printf("[%s] 创建Todo失败，JSON绑定错误: %v", requestID, err)
				c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
				return
			}
			todos = append(todos, newTodo)
			log.Printf("[%s] 创建新Todo: %+v", requestID, newTodo)
			c.JSON(http.StatusCreated, newTodo)
		})

		// PUT /api/todos/:id - 更新todo状态
		api.PUT("/todos/:id", func(c *gin.Context) {
			requestID, _ := c.Get("requestID")
			id := c.Param("id")
			var updatedTodo Todo
			if err := c.BindJSON(&updatedTodo); err != nil {
				log.Printf("[%s] 更新Todo失败，JSON绑定错误: %v", requestID, err)
				c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
				return
			}

			for i, todo := range todos {
				if todo.ID == id {
					oldTodo := todos[i]
					todos[i] = updatedTodo
					log.Printf("[%s] 更新Todo，ID: %s，旧数据: %+v，新数据: %+v", requestID, id, oldTodo, updatedTodo)
					c.JSON(http.StatusOK, updatedTodo)
					return
				}
			}
			log.Printf("[%s] 更新失败，Todo不存在，ID: %s", requestID, id)
			c.JSON(http.StatusNotFound, gin.H{"message": "Todo不存在"})
		})

		// DELETE /api/todos/:id - 删除todo
		api.DELETE("/todos/:id", func(c *gin.Context) {
			requestID, _ := c.Get("requestID")
			id := c.Param("id")
			for i, todo := range todos {
				if todo.ID == id {
					deletedTodo := todo
					todos = append(todos[:i], todos[i+1:]...)
					log.Printf("[%s] 删除Todo，ID: %s，数据: %+v", requestID, id, deletedTodo)
					c.JSON(http.StatusOK, gin.H{"message": "Todo已删除"})
					return
				}
			}
			log.Printf("[%s] 删除失败，Todo不存在，ID: %s", requestID, id)
			c.JSON(http.StatusNotFound, gin.H{"message": "Todo不存在"})
		})
	}

	// 静态文件服务
	r.Static("/static", "./static")

	// 启动服务器
	log.Println("服务器启动在端口 :8080")
	r.Run(":8080")
}

// LoggerMiddleware 自定义中间件
func LoggerMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 生成请求ID
		requestID := generateRequestID()
		c.Set("requestID", requestID)

		// 请求前
		startTime := time.Now()
		path := c.Request.URL.Path
		method := c.Request.Method
		clientIP := c.ClientIP()

		// 记录请求数据
		var requestBody []byte
		if c.Request.Body != nil {
			requestBody, _ = io.ReadAll(c.Request.Body)
			// 重新设置body，因为读取后会被消耗
			c.Request.Body = io.NopCloser(bytes.NewBuffer(requestBody))
		}

		log.Printf("[%s] 请求开始 - 方法: %s, 路径: %s, 客户端IP: %s", requestID, method, path, clientIP)
		if len(requestBody) > 0 {
			log.Printf("[%s] 请求数据: %s", requestID, string(requestBody))
		}

		// 处理请求
		c.Next()

		// 请求后
		duration := time.Since(startTime)
		statusCode := c.Writer.Status()

		log.Printf("[%s] 请求完成 - 方法: %s, 路径: %s, 状态码: %d, 耗时: %v", requestID, method, path, statusCode, duration)
	}
}
