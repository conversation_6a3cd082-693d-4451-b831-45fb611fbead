package main

import (
	"fmt"
)

// 1. 定义多个接口
type Reader interface {
	Read() string
}

type Writer interface {
	Write(text string)
}

// 2. 接口组合
type ReadWriter interface {
	Reader
	Writer
}

// 3. 实现多个接口的结构体
type File struct {
	content string
}

func (f File) Read() string {
	return f.content
}

func (f *File) Write(text string) {
	f.content = text
}

// 4. 空接口示例
func printAnything(v interface{}) {
	fmt.Printf("值: %v, 类型: %T\n", v, v)
}

// 5. 接口类型断言
func processValue(v interface{}) {
	switch val := v.(type) {
	case string:
		fmt.Printf("字符串值: %s\n", val)
	case int:
		fmt.Printf("整数值: %d\n", val)
	case Reader:
		fmt.Printf("Reader接口值: %s\n", val.Read())
	default:
		fmt.Printf("未知类型: %T\n", val)
	}
}

// 6. 定义带错误处理的接口
type DataProcessor interface {
	Process(data string) (string, error)
}

// 7. 实现带错误处理的接口
type UpperCaseProcessor struct{}

func (p UpperCaseProcessor) Process(data string) (string, error) {
	if data == "" {
		return "", fmt.Errorf("数据不能为空")
	}
	return fmt.Sprintf("处理后的数据: %s", data), nil
}

func main() {
	// 1. 基本接口使用
	file := &File{}
	file.Write("测试内容")
	fmt.Println("读取内容:", file.Read())

	// 2. 接口变量
	var reader Reader = file
	var writer Writer = file
	var readWriter ReadWriter = file

	fmt.Println("\n通过不同接口访问:")
	fmt.Println("Reader:", reader.Read())
	writer.Write("新内容")
	fmt.Println("ReadWriter:", readWriter.Read())

	// 3. 空接口使用
	fmt.Println("\n空接口示例:")
	printAnything(42)
	printAnything("Hello")
	printAnything(file)

	// 4. 类型断言示例
	fmt.Println("\n类型断言示例:")
	processValue("测试字符串")
	processValue(123)
	processValue(file)

	// 5. 错误处理接口示例
	fmt.Println("\n错误处理示例:")
	processor := UpperCaseProcessor{}
	result, err := processor.Process("测试数据")
	if err != nil {
		fmt.Println("错误:", err)
	} else {
		fmt.Println(result)
	}

	// 空数据错误处理
	result, err = processor.Process("")
	if err != nil {
		fmt.Println("错误:", err)
	} else {
		fmt.Println(result)
	}
}
