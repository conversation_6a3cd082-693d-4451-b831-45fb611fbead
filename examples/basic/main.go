package main

import (
	"fmt"
	"sync"
)

// 定义一个结构体
type Person struct {
	Name string
	Age  int
}

// 为Person结构体定义方法
func (p Person) SayHello() string {
	return fmt.Sprintf("你好，我是%s，今年%d岁", p.Name, p.Age)
}

// 定义一个接口
type Speaker interface {
	SayHello() string
}

// 演示切片操作
func demonstrateSlice() {
	// 创建切片
	numbers := []int{1, 2, 3, 4, 5}
	fmt.Println("原始切片:", numbers)

	// 追加元素
	numbers = append(numbers, 6)
	fmt.Println("追加后:", numbers)

	// 切片操作
	subSlice := numbers[1:4]
	fmt.Println("子切片:", subSlice)
}

// 演示map操作
func demonstrateMap() {
	// 创建map
	scores := map[string]int{
		"张三": 90,
		"李四": 85,
		"王五": 95,
	}

	// 遍历map
	for name, score := range scores {
		fmt.Printf("%s的分数是: %d\n", name, score)
	}

	// 检查键是否存在
	if score, exists := scores["张三"]; exists {
		fmt.Printf("张三的分数是: %d\n", score)
	}
}

func main() {
	// 创建Person实例
	//person := Person{
	//	Name: "小明",
	//	Age:  25,
	//}
	//
	//// 使用接口
	//var speaker Speaker = person
	//fmt.Println(speaker.SayHello())
	//
	//fmt.Println("\n演示切片操作:")
	//demonstrateSlice()
	//
	//fmt.Println("\n演示map操作:")
	//demonstrateMap()
	//
	//// 演示defer
	//defer fmt.Println("\n程序结束")
	//
	//// 演示错误处理
	//result, err := divide(10, 2)
	//if err != nil {
	//	fmt.Println("错误:", err)
	//} else {
	//	fmt.Println("\n10 / 2 =", result)
	//}
	//
	//result, err = divide(10, 0)
	//if err != nil {
	//	fmt.Println("错误:", err)
	//} else {
	//	fmt.Println("10 / 0 =", result)
	//}
	//map1()
	var jhonny = newPerson{
		Name:   "Jhonny",
		Age:    25,
		Adress: "123 Main St",
	}
	modify(jhonny)
	fmt.Println(jhonny)
}

// 演示错误处理
func divide(a, b int) (int, error) {
	if b == 0 {
		return 0, fmt.Errorf("除数不能为0")
	}
	return a / b, nil
}

func map1() {
	m2 := sync.Map{}

	// 存储一些测试数据
	m2.Store("key1", "value1")
	m2.Store("key2", "value2")
	m2.Store("key3", 100)
	m2.CompareAndSwap("key", nil, "value")

	// 打印所有键值对
	fmt.Println("sync.Map 中的所有数据:")
	m2.Range(func(key, value interface{}) bool {
		fmt.Printf("  %v: %v\n", key, value)
		return true // 返回 true 继续遍历，返回 false 停止遍历
	})

	// 打印特定键的值
	value, ok := m2.Load("key")
	fmt.Println("key:", value, "exists:", ok)

}

type newPerson struct {
	Name   string
	Age    int
	Adress string
}

func modify(np newPerson) {
	np.Age = 10
	np.Name = "<Jhonny>"
}

type Point struct {
	X float64
	Y float64
}

func ScaleBy(p *Point, factor float64) {
	p.Y *= factor
	p.X *= factor
}

func (p *Point) ScaleBy(factor float64) {
	p.Y *= factor
	p.X *= factor
}
