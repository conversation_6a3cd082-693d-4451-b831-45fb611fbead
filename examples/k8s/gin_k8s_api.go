package main

import (
	"context"
	"fmt"
	"log"
	"math/rand"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	networkingv1 "k8s.io/api/networking/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/clientcmd"
)

// K8sClient Kubernetes客户端结构体
type K8sClient struct {
	clientset *kubernetes.Clientset
}

// PodInfo Pod信息结构体
type PodInfo struct {
	Name              string            `json:"name"`
	Namespace         string            `json:"namespace"`
	Status            string            `json:"status"`
	NodeName          string            `json:"nodeName"`
	PodIP             string            `json:"podIP"`
	CreationTimestamp time.Time         `json:"creationTimestamp"`
	Labels            map[string]string `json:"labels"`
	Containers        []ContainerInfo   `json:"containers"`
}

// ContainerInfo 容器信息结构体
type ContainerInfo struct {
	Name  string `json:"name"`
	Image string `json:"image"`
	Ready bool   `json:"ready"`
}

// NodeInfo 节点信息结构体
type NodeInfo struct {
	Name              string            `json:"name"`
	Status            string            `json:"status"`
	Roles             []string          `json:"roles"`
	Version           string            `json:"version"`
	InternalIP        string            `json:"internalIP"`
	CreationTimestamp time.Time         `json:"creationTimestamp"`
	Labels            map[string]string `json:"labels"`
	Capacity          ResourceInfo      `json:"capacity"`
	Allocatable       ResourceInfo      `json:"allocatable"`
}

// ResourceInfo 资源信息结构体
type ResourceInfo struct {
	CPU    string `json:"cpu"`
	Memory string `json:"memory"`
	Pods   string `json:"pods"`
}

// ServiceInfo 服务信息结构体
type ServiceInfo struct {
	Name              string            `json:"name"`
	Namespace         string            `json:"namespace"`
	Type              string            `json:"type"`
	ClusterIP         string            `json:"clusterIP"`
	ExternalIP        []string          `json:"externalIP"`
	Ports             []ServicePort     `json:"ports"`
	CreationTimestamp time.Time         `json:"creationTimestamp"`
	Labels            map[string]string `json:"labels"`
}

// ServicePort 服务端口信息
type ServicePort struct {
	Name       string `json:"name"`
	Port       int32  `json:"port"`
	TargetPort string `json:"targetPort"`
	Protocol   string `json:"protocol"`
}

// DeploymentInfo Deployment信息结构体
type DeploymentInfo struct {
	Name              string            `json:"name"`
	Namespace         string            `json:"namespace"`
	Replicas          int32             `json:"replicas"`
	ReadyReplicas     int32             `json:"readyReplicas"`
	AvailableReplicas int32             `json:"availableReplicas"`
	CreationTimestamp time.Time         `json:"creationTimestamp"`
	Labels            map[string]string `json:"labels"`
	Images            []string          `json:"images"`
}

// NamespaceInfo 命名空间信息结构体
type NamespaceInfo struct {
	Name              string            `json:"name"`
	Status            string            `json:"status"`
	CreationTimestamp time.Time         `json:"creationTimestamp"`
	Labels            map[string]string `json:"labels"`
}

// PersistentVolumeInfo PV信息结构体
type PersistentVolumeInfo struct {
	Name              string            `json:"name"`
	Status            string            `json:"status"`
	Capacity          string            `json:"capacity"`
	AccessModes       []string          `json:"accessModes"`
	ReclaimPolicy     string            `json:"reclaimPolicy"`
	StorageClass      string            `json:"storageClass"`
	VolumeMode        string            `json:"volumeMode"`
	CreationTimestamp time.Time         `json:"creationTimestamp"`
	Labels            map[string]string `json:"labels"`
	ClaimRef          *PVClaimRef       `json:"claimRef,omitempty"`
}

// PVClaimRef PV声明引用
type PVClaimRef struct {
	Name      string `json:"name"`
	Namespace string `json:"namespace"`
}

// PersistentVolumeClaimInfo PVC信息结构体
type PersistentVolumeClaimInfo struct {
	Name              string            `json:"name"`
	Namespace         string            `json:"namespace"`
	Status            string            `json:"status"`
	Capacity          string            `json:"capacity"`
	AccessModes       []string          `json:"accessModes"`
	StorageClass      string            `json:"storageClass"`
	VolumeMode        string            `json:"volumeMode"`
	VolumeName        string            `json:"volumeName"`
	CreationTimestamp time.Time         `json:"creationTimestamp"`
	Labels            map[string]string `json:"labels"`
}

// IngressInfo Ingress信息结构体
type IngressInfo struct {
	Name              string            `json:"name"`
	Namespace         string            `json:"namespace"`
	IngressClass      string            `json:"ingressClass"`
	Rules             []IngressRule     `json:"rules"`
	TLS               []IngressTLS      `json:"tls"`
	CreationTimestamp time.Time         `json:"creationTimestamp"`
	Labels            map[string]string `json:"labels"`
	Annotations       map[string]string `json:"annotations"`
}

// IngressRule Ingress规则
type IngressRule struct {
	Host  string        `json:"host"`
	Paths []IngressPath `json:"paths"`
}

// IngressPath Ingress路径
type IngressPath struct {
	Path        string `json:"path"`
	PathType    string `json:"pathType"`
	ServiceName string `json:"serviceName"`
	ServicePort string `json:"servicePort"`
}

// IngressTLS Ingress TLS配置
type IngressTLS struct {
	Hosts      []string `json:"hosts"`
	SecretName string   `json:"secretName"`
}

// ConfigMapInfo ConfigMap信息结构体
type ConfigMapInfo struct {
	Name              string            `json:"name"`
	Namespace         string            `json:"namespace"`
	DataCount         int               `json:"dataCount"`
	CreationTimestamp time.Time         `json:"creationTimestamp"`
	Labels            map[string]string `json:"labels"`
	Data              map[string]string `json:"data,omitempty"`
}

// SecretInfo Secret信息结构体
type SecretInfo struct {
	Name              string            `json:"name"`
	Namespace         string            `json:"namespace"`
	Type              string            `json:"type"`
	DataCount         int               `json:"dataCount"`
	CreationTimestamp time.Time         `json:"creationTimestamp"`
	Labels            map[string]string `json:"labels"`
}

// StorageClassInfo StorageClass信息结构体
type StorageClassInfo struct {
	Name                 string            `json:"name"`
	Provisioner          string            `json:"provisioner"`
	ReclaimPolicy        string            `json:"reclaimPolicy"`
	VolumeBindingMode    string            `json:"volumeBindingMode"`
	AllowVolumeExpansion bool              `json:"allowVolumeExpansion"`
	CreationTimestamp    time.Time         `json:"creationTimestamp"`
	Labels               map[string]string `json:"labels"`
	Parameters           map[string]string `json:"parameters"`
}

// DaemonSetInfo DaemonSet信息结构体
type DaemonSetInfo struct {
	Name                   string            `json:"name"`
	Namespace              string            `json:"namespace"`
	DesiredNumberScheduled int32             `json:"desiredNumberScheduled"`
	CurrentNumberScheduled int32             `json:"currentNumberScheduled"`
	NumberReady            int32             `json:"numberReady"`
	NumberAvailable        int32             `json:"numberAvailable"`
	CreationTimestamp      time.Time         `json:"creationTimestamp"`
	Labels                 map[string]string `json:"labels"`
	Images                 []string          `json:"images"`
}

// StatefulSetInfo StatefulSet信息结构体
type StatefulSetInfo struct {
	Name              string            `json:"name"`
	Namespace         string            `json:"namespace"`
	Replicas          int32             `json:"replicas"`
	ReadyReplicas     int32             `json:"readyReplicas"`
	CurrentReplicas   int32             `json:"currentReplicas"`
	UpdatedReplicas   int32             `json:"updatedReplicas"`
	CreationTimestamp time.Time         `json:"creationTimestamp"`
	Labels            map[string]string `json:"labels"`
	Images            []string          `json:"images"`
}

// 生成请求ID
func generateRequestID() string {
	return fmt.Sprintf("REQ-%d-%04d", time.Now().Unix(), rand.Intn(10000))
}

// LoggerMiddleware 自定义日志中间件
func LoggerMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 生成请求ID
		requestID := generateRequestID()
		c.Set("requestID", requestID)

		// 请求前
		startTime := time.Now()
		path := c.Request.URL.Path
		method := c.Request.Method
		clientIP := c.ClientIP()

		log.Printf("[%s] 请求开始 - 方法: %s, 路径: %s, 客户端IP: %s", requestID, method, path, clientIP)

		// 处理请求
		c.Next()

		// 请求后
		duration := time.Since(startTime)
		statusCode := c.Writer.Status()

		log.Printf("[%s] 请求完成 - 方法: %s, 路径: %s, 状态码: %d, 耗时: %v", requestID, method, path, statusCode, duration)
	}
}

// NewK8sClient 创建新的Kubernetes客户端
func NewK8sClient(configPath string) (*K8sClient, error) {
	config, err := clientcmd.BuildConfigFromFlags("", configPath)
	if err != nil {
		return nil, fmt.Errorf("构建配置失败: %v", err)
	}

	clientset, err := kubernetes.NewForConfig(config)
	if err != nil {
		return nil, fmt.Errorf("创建客户端失败: %v", err)
	}

	return &K8sClient{clientset: clientset}, nil
}

// 获取Pod列表
func (k *K8sClient) getPods(c *gin.Context) {
	requestID, _ := c.Get("requestID")
	namespace := c.DefaultQuery("namespace", "")

	log.Printf("[%s] 获取Pod列表，命名空间: %s", requestID, namespace)

	var pods *corev1.PodList
	var err error

	if namespace == "" {
		pods, err = k.clientset.CoreV1().Pods("").List(context.TODO(), metav1.ListOptions{})
	} else {
		pods, err = k.clientset.CoreV1().Pods(namespace).List(context.TODO(), metav1.ListOptions{})
	}

	if err != nil {
		log.Printf("[%s] 获取Pod列表失败: %v", requestID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	var podInfos []PodInfo
	for _, pod := range pods.Items {
		containers := make([]ContainerInfo, 0, len(pod.Spec.Containers))
		for i, container := range pod.Spec.Containers {
			ready := false
			if i < len(pod.Status.ContainerStatuses) {
				ready = pod.Status.ContainerStatuses[i].Ready
			}
			containers = append(containers, ContainerInfo{
				Name:  container.Name,
				Image: container.Image,
				Ready: ready,
			})
		}

		podInfo := PodInfo{
			Name:              pod.Name,
			Namespace:         pod.Namespace,
			Status:            string(pod.Status.Phase),
			NodeName:          pod.Spec.NodeName,
			PodIP:             pod.Status.PodIP,
			CreationTimestamp: pod.CreationTimestamp.Time,
			Labels:            pod.Labels,
			Containers:        containers,
		}
		podInfos = append(podInfos, podInfo)
	}

	log.Printf("[%s] 成功获取Pod列表，数量: %d", requestID, len(podInfos))
	c.JSON(http.StatusOK, gin.H{
		"pods":  podInfos,
		"count": len(podInfos),
	})
}

// 获取节点列表
func (k *K8sClient) getNodes(c *gin.Context) {
	requestID, _ := c.Get("requestID")
	log.Printf("[%s] 获取节点列表", requestID)

	nodes, err := k.clientset.CoreV1().Nodes().List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		log.Printf("[%s] 获取节点列表失败: %v", requestID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	var nodeInfos []NodeInfo
	for _, node := range nodes.Items {
		// 获取节点状态
		status := "Unknown"
		for _, condition := range node.Status.Conditions {
			if condition.Type == corev1.NodeReady {
				if condition.Status == corev1.ConditionTrue {
					status = "Ready"
				} else {
					status = "NotReady"
				}
				break
			}
		}

		// 获取节点角色
		roles := make([]string, 0)
		for label := range node.Labels {
			if label == "node-role.kubernetes.io/master" || label == "node-role.kubernetes.io/control-plane" {
				roles = append(roles, "master")
			} else if label == "node-role.kubernetes.io/worker" {
				roles = append(roles, "worker")
			}
		}
		if len(roles) == 0 {
			roles = append(roles, "worker")
		}

		// 获取内部IP
		internalIP := ""
		for _, addr := range node.Status.Addresses {
			if addr.Type == corev1.NodeInternalIP {
				internalIP = addr.Address
				break
			}
		}

		nodeInfo := NodeInfo{
			Name:              node.Name,
			Status:            status,
			Roles:             roles,
			Version:           node.Status.NodeInfo.KubeletVersion,
			InternalIP:        internalIP,
			CreationTimestamp: node.CreationTimestamp.Time,
			Labels:            node.Labels,
			Capacity: ResourceInfo{
				CPU:    node.Status.Capacity.Cpu().String(),
				Memory: node.Status.Capacity.Memory().String(),
				Pods:   node.Status.Capacity.Pods().String(),
			},
			Allocatable: ResourceInfo{
				CPU:    node.Status.Allocatable.Cpu().String(),
				Memory: node.Status.Allocatable.Memory().String(),
				Pods:   node.Status.Allocatable.Pods().String(),
			},
		}
		nodeInfos = append(nodeInfos, nodeInfo)
	}

	log.Printf("[%s] 成功获取节点列表，数量: %d", requestID, len(nodeInfos))
	c.JSON(http.StatusOK, gin.H{
		"nodes": nodeInfos,
		"count": len(nodeInfos),
	})
}

// 获取服务列表
func (k *K8sClient) getServices(c *gin.Context) {
	requestID, _ := c.Get("requestID")
	namespace := c.DefaultQuery("namespace", "")

	log.Printf("[%s] 获取服务列表，命名空间: %s", requestID, namespace)

	var services *corev1.ServiceList
	var err error

	if namespace == "" {
		services, err = k.clientset.CoreV1().Services("").List(context.TODO(), metav1.ListOptions{})
	} else {
		services, err = k.clientset.CoreV1().Services(namespace).List(context.TODO(), metav1.ListOptions{})
	}

	if err != nil {
		log.Printf("[%s] 获取服务列表失败: %v", requestID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	var serviceInfos []ServiceInfo
	for _, service := range services.Items {
		ports := make([]ServicePort, 0, len(service.Spec.Ports))
		for _, port := range service.Spec.Ports {
			ports = append(ports, ServicePort{
				Name:       port.Name,
				Port:       port.Port,
				TargetPort: port.TargetPort.String(),
				Protocol:   string(port.Protocol),
			})
		}

		serviceInfo := ServiceInfo{
			Name:              service.Name,
			Namespace:         service.Namespace,
			Type:              string(service.Spec.Type),
			ClusterIP:         service.Spec.ClusterIP,
			ExternalIP:        service.Spec.ExternalIPs,
			Ports:             ports,
			CreationTimestamp: service.CreationTimestamp.Time,
			Labels:            service.Labels,
		}
		serviceInfos = append(serviceInfos, serviceInfo)
	}

	log.Printf("[%s] 成功获取服务列表，数量: %d", requestID, len(serviceInfos))
	c.JSON(http.StatusOK, gin.H{
		"services": serviceInfos,
		"count":    len(serviceInfos),
	})
}

// 获取Deployment列表
func (k *K8sClient) getDeployments(c *gin.Context) {
	requestID, _ := c.Get("requestID")
	namespace := c.DefaultQuery("namespace", "")

	log.Printf("[%s] 获取Deployment列表，命名空间: %s", requestID, namespace)

	var deployments *appsv1.DeploymentList
	var err error

	if namespace == "" {
		deployments, err = k.clientset.AppsV1().Deployments("").List(context.TODO(), metav1.ListOptions{})
	} else {
		deployments, err = k.clientset.AppsV1().Deployments(namespace).List(context.TODO(), metav1.ListOptions{})
	}

	if err != nil {
		log.Printf("[%s] 获取Deployment列表失败: %v", requestID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	var deploymentInfos []DeploymentInfo
	for _, deployment := range deployments.Items {
		// 获取镜像列表
		images := make([]string, 0)
		for _, container := range deployment.Spec.Template.Spec.Containers {
			images = append(images, container.Image)
		}

		replicas := int32(0)
		if deployment.Spec.Replicas != nil {
			replicas = *deployment.Spec.Replicas
		}

		deploymentInfo := DeploymentInfo{
			Name:              deployment.Name,
			Namespace:         deployment.Namespace,
			Replicas:          replicas,
			ReadyReplicas:     deployment.Status.ReadyReplicas,
			AvailableReplicas: deployment.Status.AvailableReplicas,
			CreationTimestamp: deployment.CreationTimestamp.Time,
			Labels:            deployment.Labels,
			Images:            images,
		}
		deploymentInfos = append(deploymentInfos, deploymentInfo)
	}

	log.Printf("[%s] 成功获取Deployment列表，数量: %d", requestID, len(deploymentInfos))
	c.JSON(http.StatusOK, gin.H{
		"deployments": deploymentInfos,
		"count":       len(deploymentInfos),
	})
}

// 获取命名空间列表
func (k *K8sClient) getNamespaces(c *gin.Context) {
	requestID, _ := c.Get("requestID")
	log.Printf("[%s] 获取命名空间列表", requestID)

	namespaces, err := k.clientset.CoreV1().Namespaces().List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		log.Printf("[%s] 获取命名空间列表失败: %v", requestID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	var namespaceInfos []NamespaceInfo
	for _, namespace := range namespaces.Items {
		namespaceInfo := NamespaceInfo{
			Name:              namespace.Name,
			Status:            string(namespace.Status.Phase),
			CreationTimestamp: namespace.CreationTimestamp.Time,
			Labels:            namespace.Labels,
		}
		namespaceInfos = append(namespaceInfos, namespaceInfo)
	}

	log.Printf("[%s] 成功获取命名空间列表，数量: %d", requestID, len(namespaceInfos))
	c.JSON(http.StatusOK, gin.H{
		"namespaces": namespaceInfos,
		"count":      len(namespaceInfos),
	})
}

// 获取集群概览信息
func (k *K8sClient) getClusterOverview(c *gin.Context) {
	requestID, _ := c.Get("requestID")
	log.Printf("[%s] 获取集群概览信息", requestID)

	// 获取节点数量
	nodes, err := k.clientset.CoreV1().Nodes().List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		log.Printf("[%s] 获取节点信息失败: %v", requestID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 获取Pod数量
	pods, err := k.clientset.CoreV1().Pods("").List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		log.Printf("[%s] 获取Pod信息失败: %v", requestID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 获取命名空间数量
	namespaces, err := k.clientset.CoreV1().Namespaces().List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		log.Printf("[%s] 获取命名空间信息失败: %v", requestID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 获取服务数量
	services, err := k.clientset.CoreV1().Services("").List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		log.Printf("[%s] 获取服务信息失败: %v", requestID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 统计Pod状态
	podStatusCount := make(map[string]int)
	for _, pod := range pods.Items {
		status := string(pod.Status.Phase)
		podStatusCount[status]++
	}

	// 统计节点状态
	nodeStatusCount := make(map[string]int)
	for _, node := range nodes.Items {
		ready := false
		for _, condition := range node.Status.Conditions {
			if condition.Type == corev1.NodeReady && condition.Status == corev1.ConditionTrue {
				ready = true
				break
			}
		}
		if ready {
			nodeStatusCount["Ready"]++
		} else {
			nodeStatusCount["NotReady"]++
		}
	}

	overview := gin.H{
		"cluster": gin.H{
			"nodes":      len(nodes.Items),
			"pods":       len(pods.Items),
			"namespaces": len(namespaces.Items),
			"services":   len(services.Items),
		},
		"nodeStatus": nodeStatusCount,
		"podStatus":  podStatusCount,
		"timestamp":  time.Now(),
	}

	log.Printf("[%s] 成功获取集群概览信息", requestID)
	c.JSON(http.StatusOK, overview)
}

// 获取PersistentVolume列表
func (k *K8sClient) getPersistentVolumes(c *gin.Context) {
	requestID, _ := c.Get("requestID")
	log.Printf("[%s] 获取PersistentVolume列表", requestID)

	pvs, err := k.clientset.CoreV1().PersistentVolumes().List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		log.Printf("[%s] 获取PersistentVolume列表失败: %v", requestID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	var pvInfos []PersistentVolumeInfo
	for _, pv := range pvs.Items {
		// 获取访问模式
		accessModes := make([]string, 0, len(pv.Spec.AccessModes))
		for _, mode := range pv.Spec.AccessModes {
			accessModes = append(accessModes, string(mode))
		}

		// 获取容量
		capacity := ""
		if storage, ok := pv.Spec.Capacity[corev1.ResourceStorage]; ok {
			capacity = storage.String()
		}

		// 获取存储类
		storageClass := ""
		if pv.Spec.StorageClassName != "" {
			storageClass = pv.Spec.StorageClassName
		}

		// 获取卷模式
		volumeMode := ""
		if pv.Spec.VolumeMode != nil {
			volumeMode = string(*pv.Spec.VolumeMode)
		}

		// 获取声明引用
		var claimRef *PVClaimRef
		if pv.Spec.ClaimRef != nil {
			claimRef = &PVClaimRef{
				Name:      pv.Spec.ClaimRef.Name,
				Namespace: pv.Spec.ClaimRef.Namespace,
			}
		}

		pvInfo := PersistentVolumeInfo{
			Name:              pv.Name,
			Status:            string(pv.Status.Phase),
			Capacity:          capacity,
			AccessModes:       accessModes,
			ReclaimPolicy:     string(pv.Spec.PersistentVolumeReclaimPolicy),
			StorageClass:      storageClass,
			VolumeMode:        volumeMode,
			CreationTimestamp: pv.CreationTimestamp.Time,
			Labels:            pv.Labels,
			ClaimRef:          claimRef,
		}
		pvInfos = append(pvInfos, pvInfo)
	}

	log.Printf("[%s] 成功获取PersistentVolume列表，数量: %d", requestID, len(pvInfos))
	c.JSON(http.StatusOK, gin.H{
		"persistentVolumes": pvInfos,
		"count":             len(pvInfos),
	})
}

// 获取PersistentVolumeClaim列表
func (k *K8sClient) getPersistentVolumeClaims(c *gin.Context) {
	requestID, _ := c.Get("requestID")
	namespace := c.DefaultQuery("namespace", "")

	log.Printf("[%s] 获取PersistentVolumeClaim列表，命名空间: %s", requestID, namespace)

	var pvcs *corev1.PersistentVolumeClaimList
	var err error

	if namespace == "" {
		pvcs, err = k.clientset.CoreV1().PersistentVolumeClaims("").List(context.TODO(), metav1.ListOptions{})
	} else {
		pvcs, err = k.clientset.CoreV1().PersistentVolumeClaims(namespace).List(context.TODO(), metav1.ListOptions{})
	}

	if err != nil {
		log.Printf("[%s] 获取PersistentVolumeClaim列表失败: %v", requestID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	var pvcInfos []PersistentVolumeClaimInfo
	for _, pvc := range pvcs.Items {
		// 获取访问模式
		accessModes := make([]string, 0, len(pvc.Spec.AccessModes))
		for _, mode := range pvc.Spec.AccessModes {
			accessModes = append(accessModes, string(mode))
		}

		// 获取容量
		capacity := ""
		if pvc.Status.Capacity != nil {
			if storage, ok := pvc.Status.Capacity[corev1.ResourceStorage]; ok {
				capacity = storage.String()
			}
		}

		// 获取存储类
		storageClass := ""
		if pvc.Spec.StorageClassName != nil {
			storageClass = *pvc.Spec.StorageClassName
		}

		// 获取卷模式
		volumeMode := ""
		if pvc.Spec.VolumeMode != nil {
			volumeMode = string(*pvc.Spec.VolumeMode)
		}

		pvcInfo := PersistentVolumeClaimInfo{
			Name:              pvc.Name,
			Namespace:         pvc.Namespace,
			Status:            string(pvc.Status.Phase),
			Capacity:          capacity,
			AccessModes:       accessModes,
			StorageClass:      storageClass,
			VolumeMode:        volumeMode,
			VolumeName:        pvc.Spec.VolumeName,
			CreationTimestamp: pvc.CreationTimestamp.Time,
			Labels:            pvc.Labels,
		}
		pvcInfos = append(pvcInfos, pvcInfo)
	}

	log.Printf("[%s] 成功获取PersistentVolumeClaim列表，数量: %d", requestID, len(pvcInfos))
	c.JSON(http.StatusOK, gin.H{
		"persistentVolumeClaims": pvcInfos,
		"count":                  len(pvcInfos),
	})
}

// 获取Ingress列表
func (k *K8sClient) getIngresses(c *gin.Context) {
	requestID, _ := c.Get("requestID")
	namespace := c.DefaultQuery("namespace", "")

	log.Printf("[%s] 获取Ingress列表，命名空间: %s", requestID, namespace)

	var ingresses *networkingv1.IngressList
	var err error

	if namespace == "" {
		ingresses, err = k.clientset.NetworkingV1().Ingresses("").List(context.TODO(), metav1.ListOptions{})
	} else {
		ingresses, err = k.clientset.NetworkingV1().Ingresses(namespace).List(context.TODO(), metav1.ListOptions{})
	}

	if err != nil {
		log.Printf("[%s] 获取Ingress列表失败: %v", requestID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	var ingressInfos []IngressInfo
	for _, ingress := range ingresses.Items {
		// 获取Ingress类
		ingressClass := ""
		if ingress.Spec.IngressClassName != nil {
			ingressClass = *ingress.Spec.IngressClassName
		}

		// 获取规则
		rules := make([]IngressRule, 0, len(ingress.Spec.Rules))
		for _, rule := range ingress.Spec.Rules {
			paths := make([]IngressPath, 0)
			if rule.HTTP != nil {
				for _, path := range rule.HTTP.Paths {
					pathType := ""
					if path.PathType != nil {
						pathType = string(*path.PathType)
					}

					serviceName := ""
					servicePort := ""
					if path.Backend.Service != nil {
						serviceName = path.Backend.Service.Name
						if path.Backend.Service.Port.Number != 0 {
							servicePort = fmt.Sprintf("%d", path.Backend.Service.Port.Number)
						} else {
							servicePort = path.Backend.Service.Port.Name
						}
					}

					paths = append(paths, IngressPath{
						Path:        path.Path,
						PathType:    pathType,
						ServiceName: serviceName,
						ServicePort: servicePort,
					})
				}
			}

			rules = append(rules, IngressRule{
				Host:  rule.Host,
				Paths: paths,
			})
		}

		// 获取TLS配置
		tlsConfigs := make([]IngressTLS, 0, len(ingress.Spec.TLS))
		for _, tls := range ingress.Spec.TLS {
			tlsConfigs = append(tlsConfigs, IngressTLS{
				Hosts:      tls.Hosts,
				SecretName: tls.SecretName,
			})
		}

		ingressInfo := IngressInfo{
			Name:              ingress.Name,
			Namespace:         ingress.Namespace,
			IngressClass:      ingressClass,
			Rules:             rules,
			TLS:               tlsConfigs,
			CreationTimestamp: ingress.CreationTimestamp.Time,
			Labels:            ingress.Labels,
			Annotations:       ingress.Annotations,
		}
		ingressInfos = append(ingressInfos, ingressInfo)
	}

	log.Printf("[%s] 成功获取Ingress列表，数量: %d", requestID, len(ingressInfos))
	c.JSON(http.StatusOK, gin.H{
		"ingresses": ingressInfos,
		"count":     len(ingressInfos),
	})
}

// 获取ConfigMap列表
func (k *K8sClient) getConfigMaps(c *gin.Context) {
	requestID, _ := c.Get("requestID")
	namespace := c.DefaultQuery("namespace", "")

	includeData := c.DefaultQuery("includeData", "false") == "true"

	log.Printf("[%s] 获取ConfigMap列表，命名空间: %s, 包含数据: %v", requestID, namespace, includeData)

	var configMaps *corev1.ConfigMapList
	var err error

	if namespace == "" {
		configMaps, err = k.clientset.CoreV1().ConfigMaps("").List(context.TODO(), metav1.ListOptions{})
	} else {
		configMaps, err = k.clientset.CoreV1().ConfigMaps(namespace).List(context.TODO(), metav1.ListOptions{})
	}

	if err != nil {
		log.Printf("[%s] 获取ConfigMap列表失败: %v", requestID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	var configMapInfos []ConfigMapInfo
	for _, cm := range configMaps.Items {
		configMapInfo := ConfigMapInfo{
			Name:              cm.Name,
			Namespace:         cm.Namespace,
			DataCount:         len(cm.Data),
			CreationTimestamp: cm.CreationTimestamp.Time,
			Labels:            cm.Labels,
		}

		// 根据参数决定是否包含数据
		if includeData {
			configMapInfo.Data = cm.Data
		}

		configMapInfos = append(configMapInfos, configMapInfo)
	}

	log.Printf("[%s] 成功获取ConfigMap列表，数量: %d", requestID, len(configMapInfos))
	c.JSON(http.StatusOK, gin.H{
		"configMaps": configMapInfos,
		"count":      len(configMapInfos),
	})
}

// 获取Secret列表
func (k *K8sClient) getSecrets(c *gin.Context) {
	requestID, _ := c.Get("requestID")
	namespace := c.DefaultQuery("namespace", "")

	log.Printf("[%s] 获取Secret列表，命名空间: %s", requestID, namespace)

	var secrets *corev1.SecretList
	var err error

	if namespace == "" {
		secrets, err = k.clientset.CoreV1().Secrets("").List(context.TODO(), metav1.ListOptions{})
	} else {
		secrets, err = k.clientset.CoreV1().Secrets(namespace).List(context.TODO(), metav1.ListOptions{})
	}

	if err != nil {
		log.Printf("[%s] 获取Secret列表失败: %v", requestID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	var secretInfos []SecretInfo
	for _, secret := range secrets.Items {
		secretInfo := SecretInfo{
			Name:              secret.Name,
			Namespace:         secret.Namespace,
			Type:              string(secret.Type),
			DataCount:         len(secret.Data),
			CreationTimestamp: secret.CreationTimestamp.Time,
			Labels:            secret.Labels,
		}
		secretInfos = append(secretInfos, secretInfo)
	}

	log.Printf("[%s] 成功获取Secret列表，数量: %d", requestID, len(secretInfos))
	c.JSON(http.StatusOK, gin.H{
		"secrets": secretInfos,
		"count":   len(secretInfos),
	})
}

// 获取StorageClass列表
func (k *K8sClient) getStorageClasses(c *gin.Context) {
	requestID, _ := c.Get("requestID")
	log.Printf("[%s] 获取StorageClass列表", requestID)

	storageClasses, err := k.clientset.StorageV1().StorageClasses().List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		log.Printf("[%s] 获取StorageClass列表失败: %v", requestID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	var storageClassInfos []StorageClassInfo
	for _, sc := range storageClasses.Items {
		// 获取回收策略
		reclaimPolicy := ""
		if sc.ReclaimPolicy != nil {
			reclaimPolicy = string(*sc.ReclaimPolicy)
		}

		// 获取卷绑定模式
		volumeBindingMode := ""
		if sc.VolumeBindingMode != nil {
			volumeBindingMode = string(*sc.VolumeBindingMode)
		}

		// 获取是否允许卷扩展
		allowVolumeExpansion := false
		if sc.AllowVolumeExpansion != nil {
			allowVolumeExpansion = *sc.AllowVolumeExpansion
		}

		storageClassInfo := StorageClassInfo{
			Name:                 sc.Name,
			Provisioner:          sc.Provisioner,
			ReclaimPolicy:        reclaimPolicy,
			VolumeBindingMode:    volumeBindingMode,
			AllowVolumeExpansion: allowVolumeExpansion,
			CreationTimestamp:    sc.CreationTimestamp.Time,
			Labels:               sc.Labels,
			Parameters:           sc.Parameters,
		}
		storageClassInfos = append(storageClassInfos, storageClassInfo)
	}

	log.Printf("[%s] 成功获取StorageClass列表，数量: %d", requestID, len(storageClassInfos))
	c.JSON(http.StatusOK, gin.H{
		"storageClasses": storageClassInfos,
		"count":          len(storageClassInfos),
	})
}

// 获取DaemonSet列表
func (k *K8sClient) getDaemonSets(c *gin.Context) {
	requestID, _ := c.Get("requestID")
	namespace := c.DefaultQuery("namespace", "")

	log.Printf("[%s] 获取DaemonSet列表，命名空间: %s", requestID, namespace)

	var daemonSets *appsv1.DaemonSetList
	var err error

	if namespace == "" {
		daemonSets, err = k.clientset.AppsV1().DaemonSets("").List(context.TODO(), metav1.ListOptions{})
	} else {
		daemonSets, err = k.clientset.AppsV1().DaemonSets(namespace).List(context.TODO(), metav1.ListOptions{})
	}

	if err != nil {
		log.Printf("[%s] 获取DaemonSet列表失败: %v", requestID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	var daemonSetInfos []DaemonSetInfo
	for _, ds := range daemonSets.Items {
		// 获取镜像列表
		images := make([]string, 0)
		for _, container := range ds.Spec.Template.Spec.Containers {
			images = append(images, container.Image)
		}

		daemonSetInfo := DaemonSetInfo{
			Name:                   ds.Name,
			Namespace:              ds.Namespace,
			DesiredNumberScheduled: ds.Status.DesiredNumberScheduled,
			CurrentNumberScheduled: ds.Status.CurrentNumberScheduled,
			NumberReady:            ds.Status.NumberReady,
			NumberAvailable:        ds.Status.NumberAvailable,
			CreationTimestamp:      ds.CreationTimestamp.Time,
			Labels:                 ds.Labels,
			Images:                 images,
		}
		daemonSetInfos = append(daemonSetInfos, daemonSetInfo)
	}

	log.Printf("[%s] 成功获取DaemonSet列表，数量: %d", requestID, len(daemonSetInfos))
	c.JSON(http.StatusOK, gin.H{
		"daemonSets": daemonSetInfos,
		"count":      len(daemonSetInfos),
	})
}

// 获取StatefulSet列表
func (k *K8sClient) getStatefulSets(c *gin.Context) {
	requestID, _ := c.Get("requestID")
	namespace := c.DefaultQuery("namespace", "")

	log.Printf("[%s] 获取StatefulSet列表，命名空间: %s", requestID, namespace)

	var statefulSets *appsv1.StatefulSetList
	var err error

	if namespace == "" {
		statefulSets, err = k.clientset.AppsV1().StatefulSets("").List(context.TODO(), metav1.ListOptions{})
	} else {
		statefulSets, err = k.clientset.AppsV1().StatefulSets(namespace).List(context.TODO(), metav1.ListOptions{})
	}

	if err != nil {
		log.Printf("[%s] 获取StatefulSet列表失败: %v", requestID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	var statefulSetInfos []StatefulSetInfo
	for _, sts := range statefulSets.Items {
		// 获取镜像列表
		images := make([]string, 0)
		for _, container := range sts.Spec.Template.Spec.Containers {
			images = append(images, container.Image)
		}

		replicas := int32(0)
		if sts.Spec.Replicas != nil {
			replicas = *sts.Spec.Replicas
		}

		statefulSetInfo := StatefulSetInfo{
			Name:              sts.Name,
			Namespace:         sts.Namespace,
			Replicas:          replicas,
			ReadyReplicas:     sts.Status.ReadyReplicas,
			CurrentReplicas:   sts.Status.CurrentReplicas,
			UpdatedReplicas:   sts.Status.UpdatedReplicas,
			CreationTimestamp: sts.CreationTimestamp.Time,
			Labels:            sts.Labels,
			Images:            images,
		}
		statefulSetInfos = append(statefulSetInfos, statefulSetInfo)
	}

	log.Printf("[%s] 成功获取StatefulSet列表，数量: %d", requestID, len(statefulSetInfos))
	c.JSON(http.StatusOK, gin.H{
		"statefulSets": statefulSetInfos,
		"count":        len(statefulSetInfos),
	})
}

func main() {
	// 初始化随机数种子
	rand.Seed(time.Now().UnixNano())

	// 创建Kubernetes客户端
	k8sClient, err := NewK8sClient("./config")
	if err != nil {
		log.Fatalf("创建Kubernetes客户端失败: %v", err)
	}

	// 创建Gin引擎
	r := gin.Default()

	// 添加日志中间件
	r.Use(LoggerMiddleware())

	// 添加CORS中间件
	r.Use(func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	})

	// API路由组
	api := r.Group("/api/k8s")
	{
		// 集群概览
		api.GET("/overview", k8sClient.getClusterOverview)

		// Pod相关接口
		api.GET("/pods", k8sClient.getPods)

		// 节点相关接口
		api.GET("/nodes", k8sClient.getNodes)

		// 服务相关接口
		api.GET("/services", k8sClient.getServices)

		// Deployment相关接口
		api.GET("/deployments", k8sClient.getDeployments)

		// 命名空间相关接口
		api.GET("/namespaces", k8sClient.getNamespaces)

		// 存储相关接口
		api.GET("/persistentvolumes", k8sClient.getPersistentVolumes)
		api.GET("/persistentvolumeclaims", k8sClient.getPersistentVolumeClaims)
		api.GET("/storageclasses", k8sClient.getStorageClasses)

		// 网络相关接口
		api.GET("/ingresses", k8sClient.getIngresses)

		// 配置相关接口
		api.GET("/configmaps", k8sClient.getConfigMaps)
		api.GET("/secrets", k8sClient.getSecrets)

		// 工作负载相关接口
		api.GET("/daemonsets", k8sClient.getDaemonSets)
		api.GET("/statefulsets", k8sClient.getStatefulSets)
	}

	// 健康检查接口
	r.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":    "healthy",
			"timestamp": time.Now(),
			"service":   "k8s-api-server",
		})
	})

	// 根路径，返回API文档
	r.GET("/", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"message":     "Kubernetes API Server",
			"version":     "2.0.0",
			"description": "完整的Kubernetes集群资源信息API",
			"endpoints": gin.H{
				"基础接口": gin.H{
					"health":   "GET /health - 健康检查",
					"overview": "GET /api/k8s/overview - 集群概览",
				},
				"核心资源": gin.H{
					"pods":       "GET /api/k8s/pods?namespace=<namespace> - Pod列表",
					"nodes":      "GET /api/k8s/nodes - 节点列表",
					"services":   "GET /api/k8s/services?namespace=<namespace> - 服务列表",
					"namespaces": "GET /api/k8s/namespaces - 命名空间列表",
				},
				"工作负载": gin.H{
					"deployments":  "GET /api/k8s/deployments?namespace=<namespace> - Deployment列表",
					"daemonsets":   "GET /api/k8s/daemonsets?namespace=<namespace> - DaemonSet列表",
					"statefulsets": "GET /api/k8s/statefulsets?namespace=<namespace> - StatefulSet列表",
				},
				"存储资源": gin.H{
					"persistentvolumes":      "GET /api/k8s/persistentvolumes - PV列表",
					"persistentvolumeclaims": "GET /api/k8s/persistentvolumeclaims?namespace=<namespace> - PVC列表",
					"storageclasses":         "GET /api/k8s/storageclasses - StorageClass列表",
				},
				"网络资源": gin.H{
					"ingresses": "GET /api/k8s/ingresses?namespace=<namespace> - Ingress列表",
				},
				"配置资源": gin.H{
					"configmaps": "GET /api/k8s/configmaps?namespace=<namespace>&includeData=<true|false> - ConfigMap列表",
					"secrets":    "GET /api/k8s/secrets?namespace=<namespace> - Secret列表",
				},
			},
			"参数说明": gin.H{
				"namespace":   "可选，指定命名空间，不指定则获取所有命名空间的资源",
				"includeData": "可选，仅用于ConfigMap，是否包含具体数据内容",
			},
		})
	})

	// 启动服务器
	port := ":8080"
	log.Printf("Kubernetes API服务器启动在端口 %s", port)
	log.Printf("访问 http://localhost%s 查看API文档", port)

	if err := r.Run(port); err != nil {
		log.Fatalf("启动服务器失败: %v", err)
	}
}
