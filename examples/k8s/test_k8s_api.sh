#!/bin/bash

BASE_URL="http://localhost:8080"

echo "=== Kubernetes API 测试脚本 ==="
echo "基础URL: $BASE_URL"
echo

# 检查服务器是否运行
echo "1. 检查服务器健康状态"
curl -s "$BASE_URL/health" | jq '.' 2>/dev/null || curl -s "$BASE_URL/health"
echo -e "\n"

echo "2. 获取API文档"
curl -s "$BASE_URL/" | jq '.' 2>/dev/null || curl -s "$BASE_URL/"
echo -e "\n"

echo "3. 获取集群概览信息"
curl -s "$BASE_URL/api/k8s/overview" | jq '.' 2>/dev/null || curl -s "$BASE_URL/api/k8s/overview"
echo -e "\n"

echo "4. 获取所有命名空间"
curl -s "$BASE_URL/api/k8s/namespaces" | jq '.' 2>/dev/null || curl -s "$BASE_URL/api/k8s/namespaces"
echo -e "\n"

echo "5. 获取所有节点信息"
curl -s "$BASE_URL/api/k8s/nodes" | jq '.' 2>/dev/null || curl -s "$BASE_URL/api/k8s/nodes"
echo -e "\n"

echo "6. 获取所有Pod信息"
curl -s "$BASE_URL/api/k8s/pods" | jq '.count, .pods[0:2]' 2>/dev/null || curl -s "$BASE_URL/api/k8s/pods"
echo -e "\n"

echo "7. 获取kube-system命名空间的Pod"
curl -s "$BASE_URL/api/k8s/pods?namespace=kube-system" | jq '.count' 2>/dev/null || curl -s "$BASE_URL/api/k8s/pods?namespace=kube-system"
echo -e "\n"

echo "8. 获取所有服务信息"
curl -s "$BASE_URL/api/k8s/services" | jq '.count, .services[0:2]' 2>/dev/null || curl -s "$BASE_URL/api/k8s/services"
echo -e "\n"

echo "9. 获取所有Deployment信息"
curl -s "$BASE_URL/api/k8s/deployments" | jq '.count, .deployments[0:2]' 2>/dev/null || curl -s "$BASE_URL/api/k8s/deployments"
echo -e "\n"

echo "10. 获取default命名空间的服务"
curl -s "$BASE_URL/api/k8s/services?namespace=default" | jq '.' 2>/dev/null || curl -s "$BASE_URL/api/k8s/services?namespace=default"
echo -e "\n"

echo "11. 获取PersistentVolume列表"
curl -s "$BASE_URL/api/k8s/persistentvolumes" | jq '.count' 2>/dev/null || curl -s "$BASE_URL/api/k8s/persistentvolumes"
echo -e "\n"

echo "12. 获取PersistentVolumeClaim列表"
curl -s "$BASE_URL/api/k8s/persistentvolumeclaims" | jq '.count' 2>/dev/null || curl -s "$BASE_URL/api/k8s/persistentvolumeclaims"
echo -e "\n"

echo "13. 获取StorageClass列表"
curl -s "$BASE_URL/api/k8s/storageclasses" | jq '.count, .storageClasses[0:2]' 2>/dev/null || curl -s "$BASE_URL/api/k8s/storageclasses"
echo -e "\n"

echo "14. 获取Ingress列表"
curl -s "$BASE_URL/api/k8s/ingresses" | jq '.count' 2>/dev/null || curl -s "$BASE_URL/api/k8s/ingresses"
echo -e "\n"

echo "15. 获取ConfigMap列表（不包含数据）"
curl -s "$BASE_URL/api/k8s/configmaps?namespace=kube-system" | jq '.count' 2>/dev/null || curl -s "$BASE_URL/api/k8s/configmaps?namespace=kube-system"
echo -e "\n"

echo "16. 获取ConfigMap列表（包含数据）"
curl -s "$BASE_URL/api/k8s/configmaps?namespace=kube-system&includeData=true" | jq '.configMaps[0].data | keys' 2>/dev/null || curl -s "$BASE_URL/api/k8s/configmaps?namespace=kube-system&includeData=true"
echo -e "\n"

echo "17. 获取Secret列表"
curl -s "$BASE_URL/api/k8s/secrets?namespace=kube-system" | jq '.count' 2>/dev/null || curl -s "$BASE_URL/api/k8s/secrets?namespace=kube-system"
echo -e "\n"

echo "18. 获取DaemonSet列表"
curl -s "$BASE_URL/api/k8s/daemonsets?namespace=kube-system" | jq '.count, .daemonSets[0:2]' 2>/dev/null || curl -s "$BASE_URL/api/k8s/daemonsets?namespace=kube-system"
echo -e "\n"

echo "19. 获取StatefulSet列表"
curl -s "$BASE_URL/api/k8s/statefulsets" | jq '.count' 2>/dev/null || curl -s "$BASE_URL/api/k8s/statefulsets"
echo -e "\n"

echo "20. 测试所有资源类型的数量统计"
echo "资源统计："
echo -n "Pods: "; curl -s "$BASE_URL/api/k8s/pods" | jq '.count' 2>/dev/null || echo "N/A"
echo -n "Nodes: "; curl -s "$BASE_URL/api/k8s/nodes" | jq '.count' 2>/dev/null || echo "N/A"
echo -n "Services: "; curl -s "$BASE_URL/api/k8s/services" | jq '.count' 2>/dev/null || echo "N/A"
echo -n "Deployments: "; curl -s "$BASE_URL/api/k8s/deployments" | jq '.count' 2>/dev/null || echo "N/A"
echo -n "DaemonSets: "; curl -s "$BASE_URL/api/k8s/daemonsets" | jq '.count' 2>/dev/null || echo "N/A"
echo -n "StatefulSets: "; curl -s "$BASE_URL/api/k8s/statefulsets" | jq '.count' 2>/dev/null || echo "N/A"
echo -n "PVs: "; curl -s "$BASE_URL/api/k8s/persistentvolumes" | jq '.count' 2>/dev/null || echo "N/A"
echo -n "PVCs: "; curl -s "$BASE_URL/api/k8s/persistentvolumeclaims" | jq '.count' 2>/dev/null || echo "N/A"
echo -n "StorageClasses: "; curl -s "$BASE_URL/api/k8s/storageclasses" | jq '.count' 2>/dev/null || echo "N/A"
echo -n "Ingresses: "; curl -s "$BASE_URL/api/k8s/ingresses" | jq '.count' 2>/dev/null || echo "N/A"
echo -n "ConfigMaps: "; curl -s "$BASE_URL/api/k8s/configmaps" | jq '.count' 2>/dev/null || echo "N/A"
echo -n "Secrets: "; curl -s "$BASE_URL/api/k8s/secrets" | jq '.count' 2>/dev/null || echo "N/A"
echo -e "\n"

echo "=== 测试完成 ==="
echo "提示: 如果安装了jq，输出会格式化显示JSON"
echo "安装jq: sudo apt-get install jq (Ubuntu/Debian) 或 brew install jq (macOS)"
echo "所有新增的资源类型API都已测试完成！"
