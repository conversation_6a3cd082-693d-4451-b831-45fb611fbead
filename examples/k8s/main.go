package main

import (
	"context"
	"fmt"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/clientcmd"
	"log"
)

func main() {
	// 配置Kubernetes客户端
	config, err := clientcmd.BuildConfigFromFlags("", "./config")
	if err != nil {
		log.Fatal(err)
	}
	// 创建Kubernetes核心客户端
	clientset, err := kubernetes.NewForConfig(config)
	if err != nil {
		log.Fatal(err)
	}
	// 获取Pod列表
	pods, err := clientset.CoreV1().Pods("namespace").List(context.TODO(), v1.ListOptions{})
	if err != nil {
		log.Fatal(err)
	}
	// 解析并处理获取到的Pod信息
	fmt.Println("Pod列表：")
	for _, pod := range pods.Items {
		fmt.Printf("名称：%s，状态：%s，创建时间：%s\n", pod.Name, pod.Status.Phase, pod.CreationTimestamp)
	}
}
