# Kubernetes API Server

基于Gin框架实现的Kubernetes集群信息获取API服务器。

## 功能特性

- 🚀 基于Gin框架的高性能HTTP API
- 📊 获取Kubernetes集群的各种资源信息
- 🔍 支持按命名空间过滤资源
- 📝 详细的请求日志记录，每个请求都有唯一ID
- 🌐 支持CORS跨域访问
- 💊 健康检查接口
- 📖 自动生成的API文档

## 支持的资源类型

### 核心资源
- **集群概览** - 集群整体状态和统计信息
- **Pod** - 容器组信息，包括状态、镜像、节点等
- **Node** - 节点信息，包括资源容量、状态等
- **Service** - 服务信息，包括端口、类型等
- **Namespace** - 命名空间信息

### 工作负载
- **Deployment** - 部署信息，包括副本数、镜像等
- **DaemonSet** - 守护进程集信息，包括调度状态等
- **StatefulSet** - 有状态应用信息，包括副本状态等

### 存储资源
- **PersistentVolume (PV)** - 持久卷信息，包括容量、访问模式等
- **PersistentVolumeClaim (PVC)** - 持久卷声明信息
- **StorageClass** - 存储类信息，包括供应商、回收策略等

### 网络资源
- **Ingress** - 入口控制器信息，包括规则、TLS配置等

### 配置资源
- **ConfigMap** - 配置映射信息，可选择是否包含数据内容
- **Secret** - 密钥信息，包括类型、数据数量等

## 前置要求

1. Go 1.16+
2. 有效的Kubernetes配置文件 (`config`)
3. 对Kubernetes集群的访问权限

## 安装依赖

```bash
go mod init k8s-api-server
go get github.com/gin-gonic/gin
go get k8s.io/client-go@latest
go get k8s.io/api@latest
go get k8s.io/apimachinery@latest
```

## 配置

确保在项目目录下有有效的Kubernetes配置文件 `config`。这个文件通常是从 `~/.kube/config` 复制而来。

## 运行

```bash
# 启动API服务器
go run gin_k8s_api.go

# 服务器将在 http://localhost:8080 启动
```

## API接口

### 基础接口

- `GET /` - API文档和接口列表
- `GET /health` - 健康检查

### 核心资源接口

- `GET /api/k8s/overview` - 集群概览信息
- `GET /api/k8s/namespaces` - 获取所有命名空间
- `GET /api/k8s/nodes` - 获取所有节点信息
- `GET /api/k8s/pods` - 获取所有Pod信息
- `GET /api/k8s/pods?namespace=<namespace>` - 获取指定命名空间的Pod
- `GET /api/k8s/services` - 获取所有服务信息
- `GET /api/k8s/services?namespace=<namespace>` - 获取指定命名空间的服务

### 工作负载接口

- `GET /api/k8s/deployments` - 获取所有Deployment信息
- `GET /api/k8s/deployments?namespace=<namespace>` - 获取指定命名空间的Deployment
- `GET /api/k8s/daemonsets` - 获取所有DaemonSet信息
- `GET /api/k8s/daemonsets?namespace=<namespace>` - 获取指定命名空间的DaemonSet
- `GET /api/k8s/statefulsets` - 获取所有StatefulSet信息
- `GET /api/k8s/statefulsets?namespace=<namespace>` - 获取指定命名空间的StatefulSet

### 存储资源接口

- `GET /api/k8s/persistentvolumes` - 获取所有PersistentVolume信息
- `GET /api/k8s/persistentvolumeclaims` - 获取所有PVC信息
- `GET /api/k8s/persistentvolumeclaims?namespace=<namespace>` - 获取指定命名空间的PVC
- `GET /api/k8s/storageclasses` - 获取所有StorageClass信息

### 网络资源接口

- `GET /api/k8s/ingresses` - 获取所有Ingress信息
- `GET /api/k8s/ingresses?namespace=<namespace>` - 获取指定命名空间的Ingress

### 配置资源接口

- `GET /api/k8s/configmaps` - 获取所有ConfigMap信息
- `GET /api/k8s/configmaps?namespace=<namespace>` - 获取指定命名空间的ConfigMap
- `GET /api/k8s/configmaps?includeData=true` - 获取ConfigMap信息并包含数据内容
- `GET /api/k8s/secrets` - 获取所有Secret信息
- `GET /api/k8s/secrets?namespace=<namespace>` - 获取指定命名空间的Secret

## 使用示例

### 基础查询

#### 获取集群概览
```bash
curl http://localhost:8080/api/k8s/overview
```

#### 获取所有Pod
```bash
curl http://localhost:8080/api/k8s/pods
```

#### 获取kube-system命名空间的Pod
```bash
curl "http://localhost:8080/api/k8s/pods?namespace=kube-system"
```

#### 获取所有节点
```bash
curl http://localhost:8080/api/k8s/nodes
```

### 存储资源查询

#### 获取所有PersistentVolume
```bash
curl http://localhost:8080/api/k8s/persistentvolumes
```

#### 获取PersistentVolumeClaim
```bash
curl http://localhost:8080/api/k8s/persistentvolumeclaims
curl "http://localhost:8080/api/k8s/persistentvolumeclaims?namespace=default"
```

#### 获取StorageClass
```bash
curl http://localhost:8080/api/k8s/storageclasses
```

### 网络资源查询

#### 获取Ingress
```bash
curl http://localhost:8080/api/k8s/ingresses
curl "http://localhost:8080/api/k8s/ingresses?namespace=default"
```

### 配置资源查询

#### 获取ConfigMap（不包含数据）
```bash
curl http://localhost:8080/api/k8s/configmaps
```

#### 获取ConfigMap（包含数据）
```bash
curl "http://localhost:8080/api/k8s/configmaps?includeData=true"
```

#### 获取Secret
```bash
curl http://localhost:8080/api/k8s/secrets
curl "http://localhost:8080/api/k8s/secrets?namespace=kube-system"
```

### 工作负载查询

#### 获取DaemonSet
```bash
curl http://localhost:8080/api/k8s/daemonsets
curl "http://localhost:8080/api/k8s/daemonsets?namespace=kube-system"
```

#### 获取StatefulSet
```bash
curl http://localhost:8080/api/k8s/statefulsets
curl "http://localhost:8080/api/k8s/statefulsets?namespace=default"
```

## 测试

运行测试脚本来验证所有API接口：

```bash
# 给脚本执行权限
chmod +x test_k8s_api.sh

# 运行测试
./test_k8s_api.sh
```

## 响应格式

所有API响应都是JSON格式，包含以下结构：

### 成功响应
```json
{
  "pods": [...],
  "count": 10
}
```

### 错误响应
```json
{
  "error": "错误描述信息"
}
```

## 日志记录

每个请求都会生成详细的日志，包括：
- 唯一的请求ID（格式：REQ-时间戳-随机数）
- 请求开始和完成时间
- 请求方法、路径、客户端IP
- 响应状态码和处理耗时
- 业务操作的详细信息

示例日志：
```
[REQ-1703123456-1234] 请求开始 - 方法: GET, 路径: /api/k8s/pods, 客户端IP: 127.0.0.1
[REQ-1703123456-1234] 获取Pod列表，命名空间: 
[REQ-1703123456-1234] 成功获取Pod列表，数量: 15
[REQ-1703123456-1234] 请求完成 - 方法: GET, 路径: /api/k8s/pods, 状态码: 200, 耗时: 45.2ms
```

## 注意事项

1. 确保Kubernetes配置文件有正确的集群访问权限
2. 某些操作可能需要较高的权限（如获取所有命名空间的资源）
3. 大型集群的资源列表可能较大，请注意网络传输时间
4. 建议在生产环境中添加认证和授权机制

## 故障排除

1. **连接失败**: 检查Kubernetes配置文件是否正确
2. **权限不足**: 确保配置的用户有足够的RBAC权限
3. **超时**: 大型集群可能需要更长的响应时间

## 扩展

可以根据需要添加更多的Kubernetes资源类型，如：
- ConfigMap
- Secret
- Ingress
- PersistentVolume
- StatefulSet
- DaemonSet
