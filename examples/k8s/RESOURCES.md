# Kubernetes 资源类型支持列表

## 📊 支持的资源类型总览

| 分类 | 资源类型 | API端点 | 支持命名空间过滤 | 特殊参数 |
|------|----------|---------|------------------|----------|
| **核心资源** | | | | |
| | Pod | `/api/k8s/pods` | ✅ | - |
| | Node | `/api/k8s/nodes` | ❌ | - |
| | Service | `/api/k8s/services` | ✅ | - |
| | Namespace | `/api/k8s/namespaces` | ❌ | - |
| **工作负载** | | | | |
| | Deployment | `/api/k8s/deployments` | ✅ | - |
| | DaemonSet | `/api/k8s/daemonsets` | ✅ | - |
| | StatefulSet | `/api/k8s/statefulsets` | ✅ | - |
| **存储资源** | | | | |
| | PersistentVolume | `/api/k8s/persistentvolumes` | ❌ | - |
| | PersistentVolumeClaim | `/api/k8s/persistentvolumeclaims` | ✅ | - |
| | StorageClass | `/api/k8s/storageclasses` | ❌ | - |
| **网络资源** | | | | |
| | Ingress | `/api/k8s/ingresses` | ✅ | - |
| **配置资源** | | | | |
| | ConfigMap | `/api/k8s/configmaps` | ✅ | `includeData=true/false` |
| | Secret | `/api/k8s/secrets` | ✅ | - |
| **特殊接口** | | | | |
| | 集群概览 | `/api/k8s/overview` | ❌ | - |

## 🔍 资源详细信息

### Pod 信息包含
- 基本信息：名称、命名空间、状态、节点名称、IP地址
- 容器信息：容器名称、镜像、就绪状态
- 元数据：创建时间、标签

### Node 信息包含
- 基本信息：名称、状态、角色、Kubernetes版本、内部IP
- 资源信息：CPU、内存、Pod容量（Capacity和Allocatable）
- 元数据：创建时间、标签

### Service 信息包含
- 基本信息：名称、命名空间、类型、ClusterIP、外部IP
- 端口信息：端口名称、端口号、目标端口、协议
- 元数据：创建时间、标签

### Deployment 信息包含
- 基本信息：名称、命名空间、副本数、就绪副本数、可用副本数
- 镜像信息：使用的容器镜像列表
- 元数据：创建时间、标签

### DaemonSet 信息包含
- 基本信息：名称、命名空间、期望调度数、当前调度数
- 状态信息：就绪数量、可用数量
- 镜像信息：使用的容器镜像列表
- 元数据：创建时间、标签

### StatefulSet 信息包含
- 基本信息：名称、命名空间、副本数、就绪副本数
- 状态信息：当前副本数、更新副本数
- 镜像信息：使用的容器镜像列表
- 元数据：创建时间、标签

### PersistentVolume 信息包含
- 基本信息：名称、状态、容量、访问模式
- 策略信息：回收策略、存储类、卷模式
- 绑定信息：声明引用（如果已绑定）
- 元数据：创建时间、标签

### PersistentVolumeClaim 信息包含
- 基本信息：名称、命名空间、状态、容量、访问模式
- 存储信息：存储类、卷模式、卷名称
- 元数据：创建时间、标签

### StorageClass 信息包含
- 基本信息：名称、供应商、回收策略
- 配置信息：卷绑定模式、是否允许卷扩展
- 参数信息：存储类参数
- 元数据：创建时间、标签

### Ingress 信息包含
- 基本信息：名称、命名空间、Ingress类
- 路由规则：主机、路径、后端服务信息
- TLS配置：主机列表、密钥名称
- 元数据：创建时间、标签、注解

### ConfigMap 信息包含
- 基本信息：名称、命名空间、数据项数量
- 数据内容：可选择是否包含具体数据（通过includeData参数控制）
- 元数据：创建时间、标签

### Secret 信息包含
- 基本信息：名称、命名空间、类型、数据项数量
- 安全考虑：不包含具体数据内容，仅显示数据项数量
- 元数据：创建时间、标签

## 🚀 新增功能特性

### 1. 存储资源完整支持
- 支持PV、PVC、StorageClass的完整信息获取
- 包含存储容量、访问模式、回收策略等详细信息
- 支持PV和PVC的绑定关系查询

### 2. 网络资源支持
- 完整的Ingress信息，包括路由规则和TLS配置
- 支持多种路径类型和后端服务配置

### 3. 配置资源安全处理
- ConfigMap支持可选的数据内容包含
- Secret信息安全处理，不暴露敏感数据

### 4. 工作负载扩展
- 新增DaemonSet和StatefulSet支持
- 完整的副本状态和调度信息

### 5. 统一的API设计
- 所有资源都支持统一的响应格式
- 一致的错误处理和日志记录
- 标准化的命名空间过滤参数

## 📈 性能优化

- 所有API调用都有详细的性能日志
- 支持并发请求处理
- 合理的数据结构设计，减少内存占用
- 可选的数据包含机制，避免不必要的数据传输

## 🔒 安全考虑

- Secret数据不会在API响应中暴露
- ConfigMap数据可选择性包含
- 所有请求都有完整的审计日志
- 支持RBAC权限控制（依赖Kubernetes配置）
