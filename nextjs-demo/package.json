{"name": "nextjs-tailwind-demo", "version": "1.0.0", "description": "Next.js + Tailwind CSS 完整学习项目", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check .", "analyze": "cross-env ANALYZE=true next build", "export": "next export"}, "dependencies": {"next": "^14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "framer-motion": "^10.16.4", "next-themes": "^0.2.1", "gray-matter": "^4.0.3", "remark": "^15.0.1", "remark-html": "^16.0.1", "date-fns": "^2.30.0"}, "devDependencies": {"@types/node": "^20.8.0", "@types/react": "^18.2.25", "@types/react-dom": "^18.2.11", "typescript": "^5.2.2", "eslint": "^8.51.0", "eslint-config-next": "^14.0.0", "prettier": "^3.0.3", "prettier-plugin-tailwindcss": "^0.5.6", "tailwindcss": "^3.3.5", "postcss": "^8.4.31", "autoprefixer": "^10.4.16", "@tailwindcss/typography": "^0.5.10", "@tailwindcss/forms": "^0.5.6", "@tailwindcss/aspect-ratio": "^0.4.2", "cross-env": "^7.0.3", "@next/bundle-analyzer": "^14.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["nextjs", "react", "tailwindcss", "typescript", "tutorial", "demo", "learning"], "author": "Your Name", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/yourusername/nextjs-tailwind-demo.git"}, "bugs": {"url": "https://github.com/yourusername/nextjs-tailwind-demo/issues"}, "homepage": "https://github.com/yourusername/nextjs-tailwind-demo#readme"}