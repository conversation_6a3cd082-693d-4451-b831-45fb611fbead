# Next.js + Tailwind CSS 完整教程

## 🚀 项目简介

这是一个完整的 Next.js 和 Tailwind CSS 学习项目，包含了从基础到高级的所有概念和实践示例。

## 📚 目录结构

```
nextjs-demo/
├── docs/                    # 教程文档
│   ├── nextjs-guide.md     # Next.js 详细教程
│   ├── tailwind-guide.md   # Tailwind CSS 详细教程
│   └── examples.md         # 实践示例
├── src/                    # 源代码
│   ├── app/               # App Router (Next.js 13+)
│   ├── components/        # 可复用组件
│   ├── styles/           # 样式文件
│   └── utils/            # 工具函数
├── public/               # 静态资源
├── package.json         # 项目配置
├── next.config.js       # Next.js 配置
├── tailwind.config.js   # Tailwind 配置
└── README.md           # 项目说明
```

## 🎯 学习目标

### Next.js 核心概念
- ✅ App Router 和 Pages Router
- ✅ 服务端渲染 (SSR)
- ✅ 静态生成 (SSG)
- ✅ 客户端渲染 (CSR)
- ✅ API Routes
- ✅ 中间件 (Middleware)
- ✅ 图片优化
- ✅ 字体优化
- ✅ 路由和导航

### Tailwind CSS 核心概念
- ✅ 实用优先的 CSS 框架
- ✅ 响应式设计
- ✅ 组件化开发
- ✅ 自定义主题
- ✅ 动画和过渡
- ✅ 暗黑模式
- ✅ 性能优化

## 🛠️ 快速开始

### 方法一：使用脚手架创建
```bash
# 创建 Next.js 项目
npx create-next-app@latest my-app --typescript --tailwind --eslint --app

# 进入项目目录
cd my-app

# 启动开发服务器
npm run dev
```

### 方法二：手动配置
```bash
# 初始化项目
npm init -y

# 安装 Next.js
npm install next@latest react@latest react-dom@latest

# 安装 Tailwind CSS
npm install -D tailwindcss postcss autoprefixer
npx tailwindcss init -p

# 安装 TypeScript (可选)
npm install -D typescript @types/react @types/node
```

## 📖 教程章节

1. **Next.js 基础** - 了解 React 框架的核心概念
2. **Tailwind CSS 基础** - 掌握实用优先的 CSS 框架
3. **路由系统** - App Router vs Pages Router
4. **数据获取** - SSR, SSG, CSR 的使用场景
5. **样式系统** - Tailwind 的高级用法
6. **组件开发** - 可复用组件的最佳实践
7. **性能优化** - 图片、字体、代码分割
8. **部署上线** - Vercel, Netlify 等平台部署

## 🎨 示例项目

- 📝 **博客系统** - 展示 SSG 和 Markdown 处理
- 🛒 **电商网站** - 展示复杂的状态管理和 API 集成
- 📊 **仪表板** - 展示数据可视化和实时更新
- 🎮 **游戏应用** - 展示客户端交互和动画

## 🔗 有用的链接

- [Next.js 官方文档](https://nextjs.org/docs)
- [Tailwind CSS 官方文档](https://tailwindcss.com/docs)
- [React 官方文档](https://react.dev)
- [TypeScript 官方文档](https://www.typescriptlang.org/docs)

## 📝 学习笔记

在学习过程中，建议：

1. **动手实践** - 每个概念都要亲自编写代码
2. **阅读文档** - 官方文档是最权威的学习资源
3. **构建项目** - 通过实际项目巩固知识
4. **社区参与** - 加入开发者社区，分享和学习

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个教程项目！
