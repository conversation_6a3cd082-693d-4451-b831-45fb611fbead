@echo off
echo ================================
echo Next.js + Tailwind CSS 学习项目
echo ================================
echo.

echo 检查 Node.js 环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到 Node.js 环境，请先安装 Node.js 18+
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

echo Node.js 环境检查通过
echo.

echo 检查 npm 环境...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到 npm 环境
    pause
    exit /b 1
)

echo npm 环境检查通过
echo.

echo 安装项目依赖...
npm install
if %errorlevel% neq 0 (
    echo 错误: 依赖安装失败
    pause
    exit /b 1
)

echo.
echo 依赖安装完成！
echo.

echo 启动开发服务器...
echo.
echo 服务地址: http://localhost:3000
echo.
echo 按 Ctrl+C 停止服务
echo.

npm run dev

pause
