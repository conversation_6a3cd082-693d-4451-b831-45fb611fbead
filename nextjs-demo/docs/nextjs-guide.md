# Next.js 完整学习指南

## 🎯 什么是 Next.js？

Next.js 是一个基于 React 的全栈框架，提供了生产级别的功能：

- **零配置** - 开箱即用的开发体验
- **混合渲染** - SSR、SSG、CSR 灵活选择
- **自动代码分割** - 按需加载，提升性能
- **内置优化** - 图片、字体、SEO 优化
- **API 路由** - 全栈开发能力
- **TypeScript 支持** - 类型安全的开发体验

## 🏗️ 核心概念

### 1. 渲染方式

#### 服务端渲染 (SSR)
```javascript
// app/page.js
export default async function Page() {
  // 每次请求都会在服务器执行
  const data = await fetch('https://api.example.com/data', {
    cache: 'no-store' // 禁用缓存
  });
  
  return <div>{data.title}</div>;
}
```

#### 静态生成 (SSG)
```javascript
// app/blog/[slug]/page.js
export async function generateStaticParams() {
  const posts = await fetch('https://api.example.com/posts');
  
  return posts.map((post) => ({
    slug: post.slug,
  }));
}

export default async function Post({ params }) {
  const post = await fetch(`https://api.example.com/posts/${params.slug}`);
  
  return <article>{post.content}</article>;
}
```

#### 客户端渲染 (CSR)
```javascript
'use client'; // 客户端组件标识

import { useState, useEffect } from 'react';

export default function ClientComponent() {
  const [data, setData] = useState(null);
  
  useEffect(() => {
    fetch('/api/data')
      .then(res => res.json())
      .then(setData);
  }, []);
  
  return <div>{data?.title}</div>;
}
```

### 2. App Router (Next.js 13+)

#### 文件系统路由
```
app/
├── page.js                 # / 路由
├── about/page.js          # /about 路由
├── blog/
│   ├── page.js            # /blog 路由
│   └── [slug]/page.js     # /blog/[slug] 动态路由
├── dashboard/
│   ├── layout.js          # 嵌套布局
│   ├── page.js            # /dashboard 路由
│   └── settings/page.js   # /dashboard/settings 路由
└── api/
    └── users/route.js     # /api/users API 路由
```

#### 布局系统
```javascript
// app/layout.js - 根布局
export default function RootLayout({ children }) {
  return (
    <html lang="zh-CN">
      <body>
        <header>全局导航</header>
        <main>{children}</main>
        <footer>全局页脚</footer>
      </body>
    </html>
  );
}

// app/dashboard/layout.js - 嵌套布局
export default function DashboardLayout({ children }) {
  return (
    <div className="flex">
      <aside>侧边栏</aside>
      <div>{children}</div>
    </div>
  );
}
```

### 3. 数据获取

#### 服务器组件中获取数据
```javascript
// app/posts/page.js
async function getPosts() {
  const res = await fetch('https://api.example.com/posts', {
    next: { revalidate: 3600 } // 1小时后重新验证
  });
  
  if (!res.ok) {
    throw new Error('Failed to fetch posts');
  }
  
  return res.json();
}

export default async function PostsPage() {
  const posts = await getPosts();
  
  return (
    <div>
      {posts.map(post => (
        <article key={post.id}>
          <h2>{post.title}</h2>
          <p>{post.excerpt}</p>
        </article>
      ))}
    </div>
  );
}
```

#### 客户端组件中获取数据
```javascript
'use client';

import { useState, useEffect } from 'react';

export default function UserProfile({ userId }) {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    async function fetchUser() {
      try {
        const res = await fetch(`/api/users/${userId}`);
        const userData = await res.json();
        setUser(userData);
      } catch (error) {
        console.error('Error fetching user:', error);
      } finally {
        setLoading(false);
      }
    }
    
    fetchUser();
  }, [userId]);
  
  if (loading) return <div>加载中...</div>;
  if (!user) return <div>用户不存在</div>;
  
  return (
    <div>
      <h1>{user.name}</h1>
      <p>{user.email}</p>
    </div>
  );
}
```

### 4. API 路由

#### 基础 API 路由
```javascript
// app/api/users/route.js
import { NextResponse } from 'next/server';

export async function GET() {
  const users = await fetchUsersFromDB();
  return NextResponse.json(users);
}

export async function POST(request) {
  const body = await request.json();
  const newUser = await createUser(body);
  return NextResponse.json(newUser, { status: 201 });
}
```

#### 动态 API 路由
```javascript
// app/api/users/[id]/route.js
export async function GET(request, { params }) {
  const { id } = params;
  const user = await getUserById(id);
  
  if (!user) {
    return NextResponse.json(
      { error: 'User not found' },
      { status: 404 }
    );
  }
  
  return NextResponse.json(user);
}

export async function PUT(request, { params }) {
  const { id } = params;
  const body = await request.json();
  const updatedUser = await updateUser(id, body);
  
  return NextResponse.json(updatedUser);
}

export async function DELETE(request, { params }) {
  const { id } = params;
  await deleteUser(id);
  
  return NextResponse.json({ message: 'User deleted' });
}
```

### 5. 中间件

```javascript
// middleware.js
import { NextResponse } from 'next/server';

export function middleware(request) {
  // 检查认证
  const token = request.cookies.get('token');
  
  if (!token && request.nextUrl.pathname.startsWith('/dashboard')) {
    return NextResponse.redirect(new URL('/login', request.url));
  }
  
  // 添加自定义头
  const response = NextResponse.next();
  response.headers.set('X-Custom-Header', 'value');
  
  return response;
}

export const config = {
  matcher: [
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};
```

## 🎨 样式和 CSS

### CSS Modules
```javascript
// components/Button.module.css
.button {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 0.25rem;
  cursor: pointer;
}

.primary {
  background-color: blue;
  color: white;
}

// components/Button.js
import styles from './Button.module.css';

export default function Button({ children, variant = 'primary' }) {
  return (
    <button className={`${styles.button} ${styles[variant]}`}>
      {children}
    </button>
  );
}
```

### Styled JSX
```javascript
export default function StyledComponent() {
  return (
    <div>
      <p>这是一个样式化的段落</p>
      <style jsx>{`
        p {
          color: blue;
          font-size: 1.2rem;
        }
      `}</style>
    </div>
  );
}
```

## 🖼️ 图片优化

```javascript
import Image from 'next/image';

export default function Gallery() {
  return (
    <div>
      {/* 本地图片 */}
      <Image
        src="/hero.jpg"
        alt="Hero image"
        width={800}
        height={600}
        priority // 优先加载
      />
      
      {/* 远程图片 */}
      <Image
        src="https://example.com/image.jpg"
        alt="Remote image"
        width={400}
        height={300}
        placeholder="blur"
        blurDataURL="data:image/jpeg;base64,..."
      />
      
      {/* 响应式图片 */}
      <Image
        src="/responsive.jpg"
        alt="Responsive image"
        fill
        style={{ objectFit: 'cover' }}
      />
    </div>
  );
}
```

## 🔤 字体优化

```javascript
// app/layout.js
import { Inter, Roboto_Mono } from 'next/font/google';

const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
});

const robotoMono = Roboto_Mono({
  subsets: ['latin'],
  display: 'swap',
});

export default function RootLayout({ children }) {
  return (
    <html lang="zh-CN" className={inter.className}>
      <body>
        <div className={robotoMono.className}>
          代码字体
        </div>
        {children}
      </body>
    </html>
  );
}
```

## 🚀 性能优化

### 代码分割
```javascript
import dynamic from 'next/dynamic';

// 动态导入组件
const DynamicComponent = dynamic(() => import('../components/Heavy'), {
  loading: () => <p>加载中...</p>,
  ssr: false, // 禁用服务端渲染
});

export default function Page() {
  return (
    <div>
      <h1>页面标题</h1>
      <DynamicComponent />
    </div>
  );
}
```

### 预加载
```javascript
import Link from 'next/link';

export default function Navigation() {
  return (
    <nav>
      <Link href="/about" prefetch={true}>
        关于我们
      </Link>
      <Link href="/contact" prefetch={false}>
        联系我们
      </Link>
    </nav>
  );
}
```

## 📱 最佳实践

1. **使用 TypeScript** - 提供类型安全
2. **合理选择渲染方式** - 根据数据特性选择 SSR/SSG/CSR
3. **优化图片和字体** - 使用 Next.js 内置优化
4. **代码分割** - 按需加载减少初始包大小
5. **SEO 优化** - 使用 Metadata API
6. **错误处理** - 实现错误边界和 404 页面
7. **性能监控** - 使用 Web Vitals 监控性能

## 🔧 配置文件

```javascript
// next.config.js
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    appDir: true,
  },
  images: {
    domains: ['example.com'],
    formats: ['image/webp', 'image/avif'],
  },
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY,
  },
  redirects: async () => {
    return [
      {
        source: '/old-path',
        destination: '/new-path',
        permanent: true,
      },
    ];
  },
};

module.exports = nextConfig;
```
