# Next.js + Tailwind CSS 实践示例

## 🎯 项目示例概览

这里包含了多个完整的项目示例，展示 Next.js 和 Tailwind CSS 的实际应用。

## 📝 1. 个人博客系统

### 功能特性
- ✅ 静态生成 (SSG) 博客文章
- ✅ Markdown 文章支持
- ✅ 响应式设计
- ✅ 暗黑模式切换
- ✅ SEO 优化
- ✅ 文章搜索和分类

### 技术栈
- Next.js 13+ (App Router)
- Tailwind CSS
- MDX (Markdown + JSX)
- Gray Matter (Front Matter 解析)
- Prism.js (代码高亮)

### 核心代码示例

#### 博客文章页面
```jsx
// app/blog/[slug]/page.js
import { getPostBySlug, getAllPosts } from '@/lib/posts';
import { MDXRemote } from 'next-mdx-remote/rsc';
import { notFound } from 'next/navigation';

export async function generateStaticParams() {
  const posts = getAllPosts();
  return posts.map((post) => ({
    slug: post.slug,
  }));
}

export async function generateMetadata({ params }) {
  const post = getPostBySlug(params.slug);
  
  if (!post) {
    return {};
  }

  return {
    title: post.title,
    description: post.excerpt,
    openGraph: {
      title: post.title,
      description: post.excerpt,
      images: [post.coverImage],
    },
  };
}

export default function BlogPost({ params }) {
  const post = getPostBySlug(params.slug);

  if (!post) {
    notFound();
  }

  return (
    <article className="max-w-4xl mx-auto px-4 py-8">
      {/* 文章头部 */}
      <header className="mb-8">
        <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
          {post.title}
        </h1>
        <div className="flex items-center text-gray-600 dark:text-gray-400 mb-6">
          <time dateTime={post.date}>
            {new Date(post.date).toLocaleDateString('zh-CN')}
          </time>
          <span className="mx-2">•</span>
          <span>{post.readingTime} 分钟阅读</span>
        </div>
        {post.coverImage && (
          <img
            src={post.coverImage}
            alt={post.title}
            className="w-full h-64 md:h-96 object-cover rounded-lg shadow-lg"
          />
        )}
      </header>

      {/* 文章内容 */}
      <div className="prose prose-lg dark:prose-invert max-w-none">
        <MDXRemote source={post.content} />
      </div>

      {/* 标签 */}
      {post.tags && (
        <div className="mt-8 pt-8 border-t border-gray-200 dark:border-gray-700">
          <div className="flex flex-wrap gap-2">
            {post.tags.map((tag) => (
              <span
                key={tag}
                className="px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full text-sm"
              >
                #{tag}
              </span>
            ))}
          </div>
        </div>
      )}
    </article>
  );
}
```

#### 博客列表页面
```jsx
// app/blog/page.js
import { getAllPosts } from '@/lib/posts';
import BlogCard from '@/components/BlogCard';
import SearchBar from '@/components/SearchBar';

export const metadata = {
  title: '博客 - 我的个人网站',
  description: '分享技术文章和生活感悟',
};

export default function BlogPage() {
  const posts = getAllPosts();

  return (
    <div className="max-w-6xl mx-auto px-4 py-8">
      <header className="text-center mb-12">
        <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
          我的博客
        </h1>
        <p className="text-xl text-gray-600 dark:text-gray-400 mb-8">
          分享技术文章和生活感悟
        </p>
        <SearchBar />
      </header>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {posts.map((post) => (
          <BlogCard key={post.slug} post={post} />
        ))}
      </div>
    </div>
  );
}
```

## 🛒 2. 电商网站

### 功能特性
- ✅ 产品展示和详情
- ✅ 购物车功能
- ✅ 用户认证
- ✅ 订单管理
- ✅ 支付集成
- ✅ 管理后台

### 核心组件

#### 产品卡片
```jsx
// components/ProductCard.jsx
import Image from 'next/image';
import Link from 'next/link';
import { useState } from 'react';
import { useCart } from '@/hooks/useCart';

export default function ProductCard({ product }) {
  const [isLoading, setIsLoading] = useState(false);
  const { addToCart } = useCart();

  const handleAddToCart = async () => {
    setIsLoading(true);
    try {
      await addToCart(product);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="group relative bg-white rounded-lg shadow-md overflow-hidden hover:shadow-xl transition-shadow duration-300">
      {/* 产品图片 */}
      <div className="relative h-64 overflow-hidden">
        <Image
          src={product.image}
          alt={product.name}
          fill
          className="object-cover group-hover:scale-105 transition-transform duration-300"
        />
        {product.discount && (
          <div className="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-sm font-bold">
            -{product.discount}%
          </div>
        )}
      </div>

      {/* 产品信息 */}
      <div className="p-4">
        <Link href={`/products/${product.id}`}>
          <h3 className="text-lg font-semibold text-gray-900 mb-2 hover:text-blue-600 transition-colors">
            {product.name}
          </h3>
        </Link>
        
        <p className="text-gray-600 text-sm mb-3 line-clamp-2">
          {product.description}
        </p>

        {/* 评分 */}
        <div className="flex items-center mb-3">
          <div className="flex text-yellow-400">
            {[...Array(5)].map((_, i) => (
              <svg
                key={i}
                className={`w-4 h-4 ${i < product.rating ? 'fill-current' : 'text-gray-300'}`}
                viewBox="0 0 20 20"
              >
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
              </svg>
            ))}
          </div>
          <span className="ml-2 text-sm text-gray-600">
            ({product.reviewCount})
          </span>
        </div>

        {/* 价格和按钮 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span className="text-2xl font-bold text-gray-900">
              ¥{product.price}
            </span>
            {product.originalPrice && (
              <span className="text-sm text-gray-500 line-through">
                ¥{product.originalPrice}
              </span>
            )}
          </div>
          
          <button
            onClick={handleAddToCart}
            disabled={isLoading || !product.inStock}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
          >
            {isLoading ? '添加中...' : product.inStock ? '加入购物车' : '缺货'}
          </button>
        </div>
      </div>
    </div>
  );
}
```

#### 购物车组件
```jsx
// components/Cart.jsx
import { useState } from 'react';
import { useCart } from '@/hooks/useCart';
import Image from 'next/image';

export default function Cart({ isOpen, onClose }) {
  const { items, updateQuantity, removeFromCart, total } = useCart();

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      {/* 背景遮罩 */}
      <div 
        className="absolute inset-0 bg-black bg-opacity-50"
        onClick={onClose}
      />
      
      {/* 购物车面板 */}
      <div className="absolute right-0 top-0 h-full w-full max-w-md bg-white shadow-xl">
        <div className="flex flex-col h-full">
          {/* 头部 */}
          <div className="flex items-center justify-between p-4 border-b">
            <h2 className="text-lg font-semibold">购物车</h2>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-full"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* 商品列表 */}
          <div className="flex-1 overflow-y-auto p-4">
            {items.length === 0 ? (
              <div className="text-center text-gray-500 mt-8">
                购物车是空的
              </div>
            ) : (
              <div className="space-y-4">
                {items.map((item) => (
                  <div key={item.id} className="flex items-center space-x-4 p-4 border rounded-lg">
                    <Image
                      src={item.image}
                      alt={item.name}
                      width={60}
                      height={60}
                      className="rounded-md"
                    />
                    
                    <div className="flex-1">
                      <h3 className="font-medium text-gray-900">{item.name}</h3>
                      <p className="text-gray-600">¥{item.price}</p>
                      
                      <div className="flex items-center mt-2">
                        <button
                          onClick={() => updateQuantity(item.id, item.quantity - 1)}
                          className="p-1 hover:bg-gray-100 rounded"
                        >
                          -
                        </button>
                        <span className="mx-3">{item.quantity}</span>
                        <button
                          onClick={() => updateQuantity(item.id, item.quantity + 1)}
                          className="p-1 hover:bg-gray-100 rounded"
                        >
                          +
                        </button>
                        <button
                          onClick={() => removeFromCart(item.id)}
                          className="ml-4 text-red-500 hover:text-red-700"
                        >
                          删除
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* 底部结算 */}
          {items.length > 0 && (
            <div className="border-t p-4">
              <div className="flex justify-between items-center mb-4">
                <span className="text-lg font-semibold">总计:</span>
                <span className="text-2xl font-bold text-blue-600">¥{total}</span>
              </div>
              <button className="w-full bg-blue-600 text-white py-3 rounded-lg hover:bg-blue-700 transition-colors">
                去结算
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
```

## 📊 3. 数据仪表板

### 功能特性
- ✅ 实时数据展示
- ✅ 图表可视化
- ✅ 响应式布局
- ✅ 数据筛选和搜索
- ✅ 导出功能

### 核心组件

#### 仪表板布局
```jsx
// app/dashboard/page.js
import StatsCard from '@/components/StatsCard';
import Chart from '@/components/Chart';
import DataTable from '@/components/DataTable';

export default function Dashboard() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部 */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <h1 className="text-3xl font-bold text-gray-900">数据仪表板</h1>
            <div className="flex space-x-4">
              <button className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                导出数据
              </button>
              <button className="bg-gray-200 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-300">
                刷新
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <StatsCard
            title="总用户数"
            value="12,345"
            change="+12%"
            changeType="increase"
            icon="users"
          />
          <StatsCard
            title="总收入"
            value="¥98,765"
            change="+8%"
            changeType="increase"
            icon="currency"
          />
          <StatsCard
            title="订单数"
            value="1,234"
            change="-3%"
            changeType="decrease"
            icon="shopping"
          />
          <StatsCard
            title="转化率"
            value="3.2%"
            change="+0.5%"
            changeType="increase"
            icon="chart"
          />
        </div>

        {/* 图表区域 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-lg font-semibold mb-4">销售趋势</h2>
            <Chart type="line" data={salesData} />
          </div>
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-lg font-semibold mb-4">用户分布</h2>
            <Chart type="pie" data={userDistribution} />
          </div>
        </div>

        {/* 数据表格 */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold">最近订单</h2>
          </div>
          <DataTable data={recentOrders} />
        </div>
      </main>
    </div>
  );
}
```

## 🎮 4. 互动游戏应用

### 功能特性
- ✅ 实时游戏状态
- ✅ 动画效果
- ✅ 响应式控制
- ✅ 分数系统
- ✅ 本地存储

### 示例：记忆卡片游戏
```jsx
// components/MemoryGame.jsx
'use client';

import { useState, useEffect } from 'react';

const CARDS = [
  '🐶', '🐱', '🐭', '🐹', '🐰', '🦊', '🐻', '🐼',
  '🐶', '🐱', '🐭', '🐹', '🐰', '🦊', '🐻', '🐼'
];

export default function MemoryGame() {
  const [cards, setCards] = useState([]);
  const [flipped, setFlipped] = useState([]);
  const [matched, setMatched] = useState([]);
  const [moves, setMoves] = useState(0);
  const [gameWon, setGameWon] = useState(false);

  // 初始化游戏
  useEffect(() => {
    initializeGame();
  }, []);

  // 检查游戏是否获胜
  useEffect(() => {
    if (matched.length === CARDS.length && matched.length > 0) {
      setGameWon(true);
    }
  }, [matched]);

  const initializeGame = () => {
    const shuffled = [...CARDS].sort(() => Math.random() - 0.5);
    setCards(shuffled.map((card, index) => ({ id: index, value: card })));
    setFlipped([]);
    setMatched([]);
    setMoves(0);
    setGameWon(false);
  };

  const handleCardClick = (id) => {
    if (flipped.length === 2 || flipped.includes(id) || matched.includes(id)) {
      return;
    }

    const newFlipped = [...flipped, id];
    setFlipped(newFlipped);

    if (newFlipped.length === 2) {
      setMoves(moves + 1);
      const [first, second] = newFlipped;
      
      if (cards[first].value === cards[second].value) {
        setMatched([...matched, first, second]);
        setFlipped([]);
      } else {
        setTimeout(() => setFlipped([]), 1000);
      }
    }
  };

  return (
    <div className="max-w-2xl mx-auto p-6">
      {/* 游戏头部 */}
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">记忆卡片游戏</h1>
        <div className="flex justify-center space-x-8 text-lg">
          <span>移动次数: <strong>{moves}</strong></span>
          <span>匹配: <strong>{matched.length / 2}</strong>/8</span>
        </div>
      </div>

      {/* 游戏获胜提示 */}
      {gameWon && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6 text-center">
          🎉 恭喜！您用 {moves} 步完成了游戏！
        </div>
      )}

      {/* 游戏网格 */}
      <div className="grid grid-cols-4 gap-4 mb-8">
        {cards.map((card) => (
          <div
            key={card.id}
            onClick={() => handleCardClick(card.id)}
            className={`
              aspect-square flex items-center justify-center text-4xl cursor-pointer rounded-lg transition-all duration-300 transform hover:scale-105
              ${
                flipped.includes(card.id) || matched.includes(card.id)
                  ? 'bg-white shadow-lg'
                  : 'bg-blue-500 hover:bg-blue-600'
              }
              ${matched.includes(card.id) ? 'ring-4 ring-green-400' : ''}
            `}
          >
            {flipped.includes(card.id) || matched.includes(card.id) ? (
              <span className="animate-flip">{card.value}</span>
            ) : (
              <span className="text-white">?</span>
            )}
          </div>
        ))}
      </div>

      {/* 控制按钮 */}
      <div className="text-center">
        <button
          onClick={initializeGame}
          className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
        >
          重新开始
        </button>
      </div>
    </div>
  );
}
```

## 🚀 部署和优化

### Vercel 部署
```bash
# 安装 Vercel CLI
npm i -g vercel

# 部署到 Vercel
vercel

# 生产环境部署
vercel --prod
```

### 性能优化清单
- ✅ 图片优化 (next/image)
- ✅ 字体优化 (next/font)
- ✅ 代码分割 (dynamic imports)
- ✅ CSS 优化 (PurgeCSS)
- ✅ 缓存策略
- ✅ SEO 优化
- ✅ Web Vitals 监控

这些示例展示了 Next.js 和 Tailwind CSS 在不同场景下的实际应用，帮助您快速上手并构建现代化的 Web 应用！
