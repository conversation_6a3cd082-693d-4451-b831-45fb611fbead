/* Tailwind CSS 基础样式 */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义基础样式 */
@layer base {
  /* HTML 和 Body 样式 */
  html {
    scroll-behavior: smooth;
  }

  body {
    @apply bg-gray-50 text-gray-900 dark:bg-gray-900 dark:text-gray-100;
    font-feature-settings: 'rlig' 1, 'calt' 1;
  }

  /* 标题样式 */
  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold tracking-tight;
  }

  h1 {
    @apply text-4xl md:text-5xl lg:text-6xl;
  }

  h2 {
    @apply text-3xl md:text-4xl lg:text-5xl;
  }

  h3 {
    @apply text-2xl md:text-3xl lg:text-4xl;
  }

  h4 {
    @apply text-xl md:text-2xl lg:text-3xl;
  }

  h5 {
    @apply text-lg md:text-xl lg:text-2xl;
  }

  h6 {
    @apply text-base md:text-lg lg:text-xl;
  }

  /* 链接样式 */
  a {
    @apply text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors;
  }

  /* 表单元素样式 */
  input, textarea, select {
    @apply border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100;
  }

  input:focus, textarea:focus, select:focus {
    @apply ring-2 ring-blue-500 border-blue-500 outline-none;
  }

  /* 按钮样式 */
  button {
    @apply transition-all duration-200 ease-in-out;
  }

  /* 代码样式 */
  code {
    @apply bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200 px-1 py-0.5 rounded text-sm font-mono;
  }

  pre {
    @apply bg-gray-100 dark:bg-gray-800 p-4 rounded-lg overflow-x-auto;
  }

  pre code {
    @apply bg-transparent p-0;
  }

  /* 滚动条样式 */
  ::-webkit-scrollbar {
    @apply w-2 h-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100 dark:bg-gray-800;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 dark:bg-gray-600 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400 dark:bg-gray-500;
  }
}

/* 自定义组件样式 */
@layer components {
  /* 按钮组件 */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-primary {
    @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
  }

  .btn-secondary {
    @apply bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500;
  }

  .btn-success {
    @apply bg-green-600 text-white hover:bg-green-700 focus:ring-green-500;
  }

  .btn-warning {
    @apply bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500;
  }

  .btn-danger {
    @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
  }

  .btn-outline {
    @apply bg-transparent border-current text-current hover:bg-current hover:text-white;
  }

  .btn-sm {
    @apply px-3 py-1.5 text-xs;
  }

  .btn-lg {
    @apply px-6 py-3 text-base;
  }

  /* 卡片组件 */
  .card {
    @apply bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 overflow-hidden;
  }

  .card-header {
    @apply px-6 py-4 border-b border-gray-200 dark:border-gray-700;
  }

  .card-body {
    @apply px-6 py-4;
  }

  .card-footer {
    @apply px-6 py-4 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600;
  }

  /* 表单组件 */
  .form-group {
    @apply mb-4;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2;
  }

  .form-input {
    @apply w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-blue-500 focus:border-blue-500;
  }

  .form-textarea {
    @apply form-input resize-vertical;
  }

  .form-select {
    @apply form-input;
  }

  .form-checkbox {
    @apply h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500;
  }

  .form-radio {
    @apply h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500;
  }

  /* 导航组件 */
  .nav {
    @apply flex space-x-4;
  }

  .nav-link {
    @apply px-3 py-2 rounded-md text-sm font-medium transition-colors;
  }

  .nav-link-active {
    @apply bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300;
  }

  .nav-link-inactive {
    @apply text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800;
  }

  /* 徽章组件 */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-primary {
    @apply bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300;
  }

  .badge-secondary {
    @apply bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300;
  }

  .badge-success {
    @apply bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300;
  }

  .badge-warning {
    @apply bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300;
  }

  .badge-danger {
    @apply bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300;
  }

  /* 警告框组件 */
  .alert {
    @apply p-4 rounded-md border;
  }

  .alert-info {
    @apply bg-blue-50 border-blue-200 text-blue-800 dark:bg-blue-900 dark:border-blue-700 dark:text-blue-300;
  }

  .alert-success {
    @apply bg-green-50 border-green-200 text-green-800 dark:bg-green-900 dark:border-green-700 dark:text-green-300;
  }

  .alert-warning {
    @apply bg-yellow-50 border-yellow-200 text-yellow-800 dark:bg-yellow-900 dark:border-yellow-700 dark:text-yellow-300;
  }

  .alert-error {
    @apply bg-red-50 border-red-200 text-red-800 dark:bg-red-900 dark:border-red-700 dark:text-red-300;
  }
}

/* 自定义实用类 */
@layer utilities {
  /* 文字阴影 */
  .text-shadow {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
  }

  .text-shadow-md {
    text-shadow: 4px 4px 8px rgba(0, 0, 0, 0.12);
  }

  .text-shadow-lg {
    text-shadow: 15px 15px 30px rgba(0, 0, 0, 0.11);
  }

  .text-shadow-none {
    text-shadow: none;
  }

  /* 渐变文字 */
  .text-gradient {
    @apply bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent;
  }

  /* 玻璃态效果 */
  .glass {
    @apply bg-white bg-opacity-20 backdrop-blur-lg border border-white border-opacity-20;
  }

  /* 悬浮效果 */
  .hover-lift {
    @apply transition-transform duration-300 ease-in-out hover:-translate-y-1 hover:shadow-lg;
  }

  /* 脉冲动画 */
  .pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  /* 淡入动画 */
  .fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  /* 滑入动画 */
  .slide-up {
    animation: slideUp 0.3s ease-out;
  }

  /* 弹入动画 */
  .bounce-in {
    animation: bounceIn 0.6s ease-out;
  }

  /* 截断文本 */
  .line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

/* 动画关键帧 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    @apply text-black bg-white;
  }
  
  a {
    @apply text-black no-underline;
  }
}
