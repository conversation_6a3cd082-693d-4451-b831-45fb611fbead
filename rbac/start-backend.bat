@echo off
chcp 65001 >nul
echo ========================================
echo    RBAC权限管理系统 - 后端启动脚本
echo ========================================
echo.

cd /d "%~dp0backend"

echo 检查Go环境...
go version >nul 2>&1
if errorlevel 1 (
    echo [错误] 未检测到Go环境，请先安装Go 1.24或更高版本
    echo 下载地址: https://golang.org/dl/
    pause
    exit /b 1
)

echo [信息] Go环境检查通过
echo.

echo 初始化Go模块...
if not exist go.mod (
    go mod init rbac-backend
)

echo 安装依赖包...
go mod tidy

echo.
echo 启动后端服务...
echo 服务地址: http://localhost:8080
echo 健康检查: http://localhost:8080/health
echo.
echo 按 Ctrl+C 停止服务
echo ========================================
echo.

go run main.go

pause
