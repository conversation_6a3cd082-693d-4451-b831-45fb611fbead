# RBAC权限管理系统 - 快速启动指南

## 系统概述

这是一个完整的基于Go+Vue的RBAC(Role-Based Access Control)权限管理系统演示，包含了现代Web应用的完整权限管理功能。

## 功能特性

### 🔐 认证授权
- JWT Token认证
- 用户登录/注销
- 权限验证中间件
- 会话管理

### 👥 用户管理
- 用户注册/登录
- 用户信息管理
- 用户状态控制
- 密码加密存储

### 🎭 角色管理
- 角色创建/编辑/删除
- 角色权限分配
- 角色状态管理
- 用户角色绑定

### 🔑 权限管理
- 权限定义和分组
- 菜单权限控制
- 操作权限控制
- 数据权限控制

## 技术栈

### 后端
- **Go 1.24** - 主要开发语言
- **Gin** - Web框架
- **GORM** - ORM框架
- **SQLite** - 数据库
- **JWT** - 认证令牌
- **bcrypt** - 密码加密

### 前端
- **Vue 3** - 前端框架
- **Element Plus** - UI组件库
- **Vue Router** - 路由管理
- **Pinia** - 状态管理
- **Axios** - HTTP客户端
- **Vite** - 构建工具

## 快速启动

### 1. 启动后端服务

```bash
# 进入后端目录
cd rbac/backend

# 初始化Go模块（如果需要）
go mod init rbac-backend

# 安装依赖
go mod tidy

# 启动后端服务
go run main.go
```

后端服务将在 `http://localhost:8080` 启动

### 2. 启动前端服务

```bash
# 进入前端目录
cd rbac/frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

前端服务将在 `http://localhost:3000` 启动

### 3. 访问系统

打开浏览器访问: `http://localhost:3000`

## 默认账户

系统预置了以下测试账户：

### 超级管理员
- **用户名**: admin
- **密码**: admin123
- **权限**: 拥有所有权限

### 普通用户
- **用户名**: user  
- **密码**: user123
- **权限**: 基础查看权限

## 主要API接口

### 认证接口
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户注销
- `GET /api/auth/profile` - 获取用户信息

### 用户管理
- `GET /api/users` - 获取用户列表
- `POST /api/users` - 创建用户
- `PUT /api/users/:id` - 更新用户
- `DELETE /api/users/:id` - 删除用户

### 角色管理
- `GET /api/roles` - 获取角色列表
- `POST /api/roles` - 创建角色
- `PUT /api/roles/:id` - 更新角色
- `DELETE /api/roles/:id` - 删除角色

### 权限管理
- `GET /api/permissions` - 获取权限列表
- `POST /api/permissions` - 创建权限
- `PUT /api/permissions/:id` - 更新权限
- `DELETE /api/permissions/:id` - 删除权限

## 项目结构

```
rbac/
├── backend/                 # Go后端
│   ├── main.go             # 主程序入口
│   ├── models/             # 数据模型
│   ├── handlers/           # 路由处理器
│   ├── middleware/         # 中间件
│   ├── database/           # 数据库相关
│   └── utils/              # 工具函数
├── frontend/               # Vue前端
│   ├── src/
│   │   ├── components/     # 组件
│   │   ├── views/          # 页面
│   │   ├── router/         # 路由
│   │   ├── stores/         # 状态管理
│   │   └── utils/          # 工具函数
│   ├── public/
│   └── package.json
└── README.md
```

## 数据库设计

### 核心表结构
- **users** - 用户表
- **roles** - 角色表  
- **permissions** - 权限表
- **user_roles** - 用户角色关联表
- **role_permissions** - 角色权限关联表

## 权限控制说明

### 菜单权限
- 根据用户角色动态生成菜单
- 无权限菜单自动隐藏

### 操作权限  
- 按钮级别的权限控制
- API接口权限验证

### 数据权限
- 基于角色的数据访问控制
- 支持部门级数据隔离

## 开发说明

### 添加新权限
1. 在数据库中添加权限记录
2. 在前端路由中配置权限标识
3. 在组件中使用权限检查

### 添加新角色
1. 创建角色
2. 分配相应权限
3. 将用户绑定到角色

### 自定义权限验证
```javascript
// 前端权限检查
if (userStore.hasPermission('user:create')) {
  // 有权限的操作
}

// 后端权限验证
middleware.PermissionMiddleware("user:create")
```

## 注意事项

1. **数据库文件**: 系统使用SQLite，数据库文件为 `rbac.db`
2. **JWT密钥**: 生产环境请修改JWT密钥
3. **密码安全**: 系统使用bcrypt加密密码
4. **CORS配置**: 已配置跨域支持
5. **错误处理**: 完整的错误处理和用户提示

## 扩展建议

1. **数据库**: 可替换为MySQL/PostgreSQL
2. **缓存**: 可添加Redis缓存
3. **日志**: 可集成日志系统
4. **监控**: 可添加系统监控
5. **部署**: 可使用Docker容器化部署

## 故障排除

### 常见问题

1. **端口占用**: 确保8080和3000端口未被占用
2. **依赖安装**: 确保Go和Node.js环境正确安装
3. **权限问题**: 检查用户是否有相应权限
4. **网络问题**: 检查前后端通信是否正常

### 日志查看

- 后端日志: 控制台输出
- 前端日志: 浏览器开发者工具
- 数据库日志: GORM日志输出

这个RBAC系统提供了完整的权限管理功能，适合用于学习、原型开发或作为项目基础。
