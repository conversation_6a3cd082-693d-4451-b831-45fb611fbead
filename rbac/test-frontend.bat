@echo off
chcp 65001 >nul
echo ========================================
echo    测试前端启动
echo ========================================
echo.

cd /d "%~dp0frontend"

echo 检查Node.js环境...
node --version >nul 2>&1
if errorlevel 1 (
    echo [错误] 未检测到Node.js环境
    pause
    exit /b 1
)

echo [信息] Node.js环境检查通过
echo.

echo 清理依赖...
if exist node_modules (
    rmdir /s /q node_modules
)

if exist package-lock.json (
    del package-lock.json
)

echo 重新安装依赖...
npm install
if errorlevel 1 (
    echo [错误] 依赖安装失败
    pause
    exit /b 1
)

echo.
echo 启动开发服务器...
npm run dev

pause
