@echo off
chcp 65001 >nul
title RBAC权限管理系统启动器

:menu
cls
echo ========================================
echo        RBAC权限管理系统启动器
echo ========================================
echo.
echo 请选择要启动的服务:
echo.
echo [1] 启动后端服务 (Go)
echo [2] 启动前端服务 (Vue)
echo [3] 同时启动前后端服务
echo [4] 查看系统信息
echo [5] 退出
echo.
echo ========================================

set /p choice=请输入选项 (1-5): 

if "%choice%"=="1" goto backend
if "%choice%"=="2" goto frontend
if "%choice%"=="3" goto both
if "%choice%"=="4" goto info
if "%choice%"=="5" goto exit
echo 无效选项，请重新选择
pause
goto menu

:backend
echo.
echo 启动后端服务...
start "RBAC后端服务" cmd /k "cd /d "%~dp0" && start-backend.bat"
echo 后端服务启动中，请查看新窗口...
pause
goto menu

:frontend
echo.
echo 启动前端服务...
start "RBAC前端服务" cmd /k "cd /d "%~dp0" && start-frontend.bat"
echo 前端服务启动中，请查看新窗口...
pause
goto menu

:both
echo.
echo 同时启动前后端服务...
echo 正在启动后端服务...
start "RBAC后端服务" cmd /k "cd /d "%~dp0" && start-backend.bat"
timeout /t 3 /nobreak >nul
echo 正在启动前端服务...
start "RBAC前端服务" cmd /k "cd /d "%~dp0" && start-frontend.bat"
echo.
echo 前后端服务启动中，请查看新窗口...
echo 请等待服务完全启动后访问: http://localhost:3000
pause
goto menu

:info
cls
echo ========================================
echo           系统信息
echo ========================================
echo.
echo 项目名称: RBAC权限管理系统
echo 技术栈:   Go + Vue3 + Element Plus
echo 数据库:   SQLite
echo 认证:     JWT Token
echo.
echo 服务地址:
echo   后端API:  http://localhost:8080
echo   前端界面: http://localhost:3000
echo   健康检查: http://localhost:8080/health
echo.
echo 默认账户:
echo   超级管理员: admin / admin123
echo   普通用户:   user / user123
echo.
echo 主要功能:
echo   - 用户管理 (增删改查)
echo   - 角色管理 (权限分配)
echo   - 权限管理 (细粒度控制)
echo   - JWT认证 (安全登录)
echo   - 响应式界面 (多设备支持)
echo.
echo 项目结构:
echo   backend/  - Go后端代码
echo   frontend/ - Vue前端代码
echo.
echo ========================================
pause
goto menu

:exit
echo.
echo 感谢使用RBAC权限管理系统！
echo.
pause
exit

:error
echo 发生错误，请检查环境配置
pause
goto menu
