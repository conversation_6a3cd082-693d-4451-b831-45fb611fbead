-- RBAC权限管理系统数据库初始化脚本

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    avatar VARCHAR(255),
    status INTEGER DEFAULT 1,
    last_login DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    deleted_at DATETIME
);

-- 创建角色表
CREATE TABLE IF NOT EXISTS roles (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(50) NOT NULL UNIQUE,
    description VARCHAR(255),
    status INTEGER DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    deleted_at DATETIME
);

-- 创建权限表
CREATE TABLE IF NOT EXISTS permissions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VA<PERSON>HA<PERSON>(50) NOT NULL,
    code VARCHAR(100) NOT NULL UNIQUE,
    type VARCHAR(20) NOT NULL,
    description VARCHAR(255),
    parent_id INTEGER,
    sort INTEGER DEFAULT 0,
    icon VARCHAR(50),
    path VARCHAR(255),
    component VARCHAR(255),
    status INTEGER DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    deleted_at DATETIME,
    FOREIGN KEY (parent_id) REFERENCES permissions(id)
);

-- 创建用户角色关联表
CREATE TABLE IF NOT EXISTS user_roles (
    user_id INTEGER NOT NULL,
    role_id INTEGER NOT NULL,
    PRIMARY KEY (user_id, role_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE
);

-- 创建角色权限关联表
CREATE TABLE IF NOT EXISTS role_permissions (
    role_id INTEGER NOT NULL,
    permission_id INTEGER NOT NULL,
    PRIMARY KEY (role_id, permission_id),
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE
);

-- 插入默认权限数据
INSERT OR IGNORE INTO permissions (name, code, type, description, parent_id, sort, icon, path, component) VALUES
('仪表盘', 'dashboard:view', 'menu', '查看仪表盘', NULL, 1, 'Odometer', '/dashboard', 'Dashboard'),
('系统管理', 'system:manage', 'menu', '系统管理菜单', NULL, 2, 'Setting', '/system', NULL),
('用户管理', 'user:read', 'menu', '查看用户列表', 2, 1, 'User', '/system/users', 'UserManagement'),
('用户创建', 'user:create', 'button', '创建新用户', 3, 1, NULL, NULL, NULL),
('用户更新', 'user:update', 'button', '更新用户信息', 3, 2, NULL, NULL, NULL),
('用户删除', 'user:delete', 'button', '删除用户', 3, 3, NULL, NULL, NULL),
('角色管理', 'role:read', 'menu', '查看角色列表', 2, 2, 'UserFilled', '/system/roles', 'RoleManagement'),
('角色创建', 'role:create', 'button', '创建新角色', 7, 1, NULL, NULL, NULL),
('角色更新', 'role:update', 'button', '更新角色信息', 7, 2, NULL, NULL, NULL),
('角色删除', 'role:delete', 'button', '删除角色', 7, 3, NULL, NULL, NULL),
('权限管理', 'permission:read', 'menu', '查看权限列表', 2, 3, 'Key', '/system/permissions', 'PermissionManagement'),
('权限创建', 'permission:create', 'button', '创建新权限', 11, 1, NULL, NULL, NULL),
('权限更新', 'permission:update', 'button', '更新权限信息', 11, 2, NULL, NULL, NULL),
('权限删除', 'permission:delete', 'button', '删除权限', 11, 3, NULL, NULL, NULL);

-- 插入默认角色数据
INSERT OR IGNORE INTO roles (name, description, status) VALUES
('超级管理员', '拥有所有权限的管理员', 1),
('普通用户', '普通用户角色', 1),
('访客', '只读访问权限', 1);

-- 插入默认用户数据 (密码都是经过bcrypt加密的 "admin123" 和 "user123")
INSERT OR IGNORE INTO users (username, email, password, status) VALUES
('admin', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 1),
('user', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 1);

-- 为超级管理员分配所有权限
INSERT OR IGNORE INTO role_permissions (role_id, permission_id)
SELECT 1, id FROM permissions;

-- 为普通用户分配基础权限
INSERT OR IGNORE INTO role_permissions (role_id, permission_id) VALUES
(2, 1), -- 仪表盘
(2, 3); -- 用户查看

-- 为访客分配只读权限
INSERT OR IGNORE INTO role_permissions (role_id, permission_id) VALUES
(3, 1); -- 仪表盘

-- 为用户分配角色
INSERT OR IGNORE INTO user_roles (user_id, role_id) VALUES
(1, 1), -- admin -> 超级管理员
(2, 2); -- user -> 普通用户

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_status ON users(status);
CREATE INDEX IF NOT EXISTS idx_roles_name ON roles(name);
CREATE INDEX IF NOT EXISTS idx_roles_status ON roles(status);
CREATE INDEX IF NOT EXISTS idx_permissions_code ON permissions(code);
CREATE INDEX IF NOT EXISTS idx_permissions_type ON permissions(type);
CREATE INDEX IF NOT EXISTS idx_permissions_parent_id ON permissions(parent_id);
CREATE INDEX IF NOT EXISTS idx_user_roles_user_id ON user_roles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_roles_role_id ON user_roles(role_id);
CREATE INDEX IF NOT EXISTS idx_role_permissions_role_id ON role_permissions(role_id);
CREATE INDEX IF NOT EXISTS idx_role_permissions_permission_id ON role_permissions(permission_id);
