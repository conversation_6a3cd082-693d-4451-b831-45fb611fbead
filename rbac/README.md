# RBAC权限管理系统

基于Go+Vue的完整RBAC(Role-Based Access Control)权限管理系统演示。

## 功能特性

### 🔐 认证授权
- JWT Token认证
- 用户登录/注销
- 权限验证中间件
- 会话管理

### 👥 用户管理
- 用户注册/登录
- 用户信息管理
- 用户状态控制
- 密码加密存储

### 🎭 角色管理
- 角色创建/编辑/删除
- 角色权限分配
- 角色状态管理
- 用户角色绑定

### 🔑 权限管理
- 权限定义和分组
- 菜单权限控制
- 操作权限控制
- 数据权限控制

### 🎨 管理界面
- 现代化Admin后台界面
- 响应式设计
- 菜单权限动态加载
- 操作日志记录

## 技术栈

### 后端
- **Go 1.24** - 主要开发语言
- **Gin** - Web框架
- **GORM** - ORM框架
- **SQLite** - 数据库
- **JWT** - 认证令牌
- **bcrypt** - 密码加密

### 前端
- **Vue 3** - 前端框架
- **Element Plus** - UI组件库
- **Vue Router** - 路由管理
- **Pinia** - 状态管理
- **Axios** - HTTP客户端
- **Vite** - 构建工具

## 项目结构

```
rbac/
├── backend/                 # Go后端
│   ├── main.go             # 主程序入口
│   ├── models/             # 数据模型
│   │   ├── user.go         # 用户模型
│   │   ├── role.go         # 角色模型
│   │   └── permission.go   # 权限模型
│   ├── handlers/           # 路由处理器
│   │   ├── auth.go         # 认证相关
│   │   ├── user.go         # 用户管理
│   │   ├── role.go         # 角色管理
│   │   └── permission.go   # 权限管理
│   ├── middleware/         # 中间件
│   │   ├── auth.go         # 认证中间件
│   │   └── cors.go         # 跨域中间件
│   ├── database/           # 数据库
│   │   └── database.go     # 数据库连接
│   └── utils/              # 工具函数
│       └── jwt.go          # JWT工具
├── frontend/               # Vue前端
│   ├── src/
│   │   ├── components/     # 组件
│   │   ├── views/          # 页面
│   │   ├── router/         # 路由
│   │   ├── stores/         # 状态管理
│   │   └── utils/          # 工具函数
│   ├── public/
│   └── package.json
└── README.md
```

## 快速开始

### 1. 启动后端

```bash
cd rbac/backend
go mod init rbac-backend
go mod tidy
go run main.go
```

后端服务将在 `http://localhost:8080` 启动

### 2. 启动前端

```bash
cd rbac/frontend
npm install
npm run dev
```

前端服务将在 `http://localhost:3000` 启动

### 3. 默认账户

- **超级管理员**: admin / admin123
- **普通用户**: user / user123

## API接口

### 认证接口
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户注销
- `GET /api/auth/profile` - 获取用户信息

### 用户管理
- `GET /api/users` - 获取用户列表
- `POST /api/users` - 创建用户
- `PUT /api/users/:id` - 更新用户
- `DELETE /api/users/:id` - 删除用户

### 角色管理
- `GET /api/roles` - 获取角色列表
- `POST /api/roles` - 创建角色
- `PUT /api/roles/:id` - 更新角色
- `DELETE /api/roles/:id` - 删除角色

### 权限管理
- `GET /api/permissions` - 获取权限列表
- `POST /api/permissions` - 创建权限
- `PUT /api/permissions/:id` - 更新权限
- `DELETE /api/permissions/:id` - 删除权限

## 数据库设计

### 用户表 (users)
- id, username, email, password, status, created_at, updated_at

### 角色表 (roles)
- id, name, description, status, created_at, updated_at

### 权限表 (permissions)
- id, name, code, type, description, created_at, updated_at

### 用户角色关联表 (user_roles)
- user_id, role_id

### 角色权限关联表 (role_permissions)
- role_id, permission_id

## 权限控制

### 菜单权限
- 根据用户角色动态生成菜单
- 无权限菜单自动隐藏

### 操作权限
- 按钮级别的权限控制
- API接口权限验证

### 数据权限
- 基于角色的数据访问控制
- 支持部门级数据隔离

## 开发说明

这是一个完整的RBAC权限管理系统演示，包含了现代Web应用的常见权限管理需求。代码结构清晰，易于理解和扩展。

适合用于：
- 学习RBAC权限设计
- 快速搭建管理后台
- 权限系统原型开发
- Go+Vue全栈开发参考
