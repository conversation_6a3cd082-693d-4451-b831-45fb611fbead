import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import NProgress from 'nprogress'

// 导入页面组件
const Login = () => import('@/views/Login.vue')
const Layout = () => import('@/layout/index.vue')
const Dashboard = () => import('@/views/Dashboard.vue')
const UserManagement = () => import('@/views/system/UserManagement.vue')
const RoleManagement = () => import('@/views/system/RoleManagement.vue')
const PermissionManagement = () => import('@/views/system/PermissionManagement.vue')
const Profile = () => import('@/views/Profile.vue')
const NotFound = () => import('@/views/404.vue')

// 基础路由（不需要权限验证）
const constantRoutes = [
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: {
      title: '登录',
      hidden: true
    }
  },
  {
    path: '/404',
    name: 'NotFound',
    component: NotFound,
    meta: {
      title: '页面不存在',
      hidden: true
    }
  }
]

// 需要权限验证的路由
const asyncRoutes = [
  {
    path: '/dashboard',
    component: Layout,
    children: [
      {
        path: '',
        name: 'Dashboard',
        component: Dashboard,
        meta: {
          title: '仪表盘',
          icon: 'Odometer',
          permission: 'dashboard:view'
        }
      }
    ]
  },
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/system',
    component: Layout,
    redirect: '/system/users',
    meta: {
      title: '系统管理',
      icon: 'Setting',
      permission: 'system:manage'
    },
    children: [
      {
        path: '/system/users',
        name: 'UserManagement',
        component: UserManagement,
        meta: {
          title: '用户管理',
          icon: 'User',
          permission: 'user:read'
        }
      },
      {
        path: '/system/roles',
        name: 'RoleManagement',
        component: RoleManagement,
        meta: {
          title: '角色管理',
          icon: 'UserFilled',
          permission: 'role:read'
        }
      },
      {
        path: '/system/permissions',
        name: 'PermissionManagement',
        component: PermissionManagement,
        meta: {
          title: '权限管理',
          icon: 'Key',
          permission: 'permission:read'
        }
      }
    ]
  },
  {
    path: '/profile',
    component: Layout,
    children: [
      {
        path: '/profile',
        name: 'Profile',
        component: Profile,
        meta: {
          title: '个人中心',
          icon: 'User',
          hidden: true
        }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404',
    meta: {
      hidden: true
    }
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes: [...constantRoutes, ...asyncRoutes],
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  // 开始进度条
  NProgress.start()

  const userStore = useUserStore()

  // 设置页面标题
  document.title = to.meta.title ? `${to.meta.title} - RBAC权限管理系统` : 'RBAC权限管理系统'

  // 白名单路由（不需要登录）
  const whiteList = ['/login', '/404']

  if (userStore.isLoggedIn) {
    // 已登录
    if (to.path === '/login') {
      // 如果已登录，访问登录页则重定向到首页
      next('/')
    } else {
      // 检查是否有用户信息
      if (!userStore.userInfo) {
        try {
          // 获取用户信息
          await userStore.getUserInfo()
          next()
        } catch (error) {
          // 获取用户信息失败，跳转到登录页
          ElMessage.error('获取用户信息失败，请重新登录')
          userStore.logout()
          next('/login')
        }
      } else {
        // 检查权限
        if (to.meta.permission && !userStore.hasPermission(to.meta.permission)) {
          ElMessage.error('权限不足')
          next('/404')
        } else {
          next()
        }
      }
    }
  } else {
    // 未登录
    if (whiteList.includes(to.path)) {
      // 在白名单中，直接访问
      next()
    } else {
      // 不在白名单中，跳转到登录页
      next('/login')
    }
  }
})

router.afterEach(() => {
  // 结束进度条
  NProgress.done()
})

// 导出路由实例和路由配置
export default router
export { asyncRoutes }
