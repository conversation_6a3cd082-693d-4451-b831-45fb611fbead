<template>
  <div v-if="!item.meta?.hidden">
    <!-- 有子菜单的情况 -->
    <el-sub-menu
      v-if="hasChildren"
      :index="resolvePath(item.path)"
    >
      <template #title>
        <el-icon v-if="item.meta?.icon">
          <component :is="item.meta.icon" />
        </el-icon>
        <span>{{ item.meta?.title }}</span>
      </template>
      
      <sidebar-item
        v-for="child in visibleChildren"
        :key="child.path"
        :item="child"
        :base-path="''"
      />
    </el-sub-menu>
    
    <!-- 没有子菜单的情况 - 处理一级菜单 -->
    <el-menu-item
      v-else
      :index="getMenuIndex()"
    >
      <el-icon v-if="getMenuMeta().icon">
        <component :is="getMenuMeta().icon" />
      </el-icon>
      <template #title>
        <span>{{ getMenuMeta().title }}</span>
      </template>
    </el-menu-item>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  item: {
    type: Object,
    required: true
  },
  basePath: {
    type: String,
    default: ''
  }
})

// 计算可见的子菜单
const visibleChildren = computed(() => {
  const children = props.item.children
  if (!children || children.length === 0) {
    return []
  }
  
  // 过滤掉隐藏的子菜单
  return children.filter(child => !child.meta?.hidden)
})

// 计算是否有子菜单
const hasChildren = computed(() => {
  return visibleChildren.value.length > 0
})

// 解析路径
function resolvePath(routePath) {
  // 对于一级菜单，如果path为空字符串，使用父路由的path
  if (routePath === '' && props.item.path) {
    return props.item.path
  }
  // 直接返回路径，因为我们的路由都是绝对路径
  return routePath
}

// 获取菜单索引
function getMenuIndex() {
  // 如果是一级菜单且有子路由，使用第一个子路由的路径
  if (!hasChildren.value && props.item.children && props.item.children.length > 0) {
    const firstChild = props.item.children[0]
    return firstChild.path || props.item.path
  }
  return resolvePath(props.item.path)
}

// 获取菜单元数据
function getMenuMeta() {
  // 如果是一级菜单且有子路由，使用第一个子路由的meta
  if (!hasChildren.value && props.item.children && props.item.children.length > 0) {
    const firstChild = props.item.children[0]
    return firstChild.meta || props.item.meta || {}
  }
  return props.item.meta || {}
}
</script>
