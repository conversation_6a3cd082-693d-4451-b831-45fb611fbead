<template>
  <div class="layout-container">
    <el-container class="full-height">
      <!-- 侧边栏 -->
      <el-aside :width="sidebarWidth" class="sidebar">
        <div class="logo">
          <div class="logo-icon">
            <el-icon :size="32" color="#409eff">
              <Setting />
            </el-icon>
          </div>
          <span v-if="!isCollapse" class="logo-text">RBAC系统</span>
        </div>

        <el-menu
          :default-active="activeMenu"
          :collapse="isCollapse"
          :unique-opened="true"
          router
          class="sidebar-menu"
          background-color="#304156"
          text-color="#bfcbd9"
          active-text-color="#fff"
        >
          <sidebar-item
            v-for="route in menuRoutes"
            :key="route.path"
            :item="route"
            :base-path="''"
          />
        </el-menu>
      </el-aside>

      <!-- 主体内容 -->
      <el-container class="main-container">
        <!-- 顶部导航 -->
        <el-header class="header">
          <div class="header-left">
            <el-button
              type="text"
              @click="toggleSidebar"
              class="collapse-btn"
            >
              <el-icon><Expand v-if="isCollapse" /><Fold v-else /></el-icon>
            </el-button>

            <el-breadcrumb separator="/">
              <el-breadcrumb-item
                v-for="item in breadcrumbs"
                :key="item.path"
                :to="item.path"
              >
                {{ item.title }}
              </el-breadcrumb-item>
            </el-breadcrumb>
          </div>

          <div class="header-right">
            <el-dropdown @command="handleCommand">
              <div class="user-info">
                <el-avatar :size="32" :src="userStore.avatar">
                  <el-icon><User /></el-icon>
                </el-avatar>
                <span class="username">{{ userStore.username }}</span>
                <el-icon class="arrow-down"><ArrowDown /></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">
                    <el-icon><User /></el-icon>
                    个人中心
                  </el-dropdown-item>
                  <el-dropdown-item command="changePassword">
                    <el-icon><Key /></el-icon>
                    修改密码
                  </el-dropdown-item>
                  <el-dropdown-item divided command="logout">
                    <el-icon><SwitchButton /></el-icon>
                    退出登录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>

        <!-- 主要内容区域 -->
        <el-main class="main-content">
          <router-view v-slot="{ Component }">
            <transition name="fade" mode="out-in">
              <component :is="Component" />
            </transition>
          </router-view>
        </el-main>
      </el-container>
    </el-container>

    <!-- 修改密码对话框 -->
    <change-password-dialog
      v-model="showChangePassword"
      @success="handlePasswordChanged"
    />
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessageBox, ElMessage } from 'element-plus'
import SidebarItem from './components/SidebarItem.vue'
import ChangePasswordDialog from './components/ChangePasswordDialog.vue'
import { asyncRoutes } from '@/router'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const isCollapse = ref(false)
const showChangePassword = ref(false)

// 计算属性
const sidebarWidth = computed(() => isCollapse.value ? '64px' : '250px')
const activeMenu = computed(() => route.path)

// 根据权限过滤菜单路由
const menuRoutes = computed(() => {
  const filtered = filterRoutes(asyncRoutes)
  console.log('Filtered menu routes:', filtered)
  return filtered
})

// 面包屑导航
const breadcrumbs = computed(() => {
  const matched = route.matched.filter(item => item.meta && item.meta.title)
  return matched.map(item => ({
    path: item.path,
    title: item.meta.title
  }))
})

// 过滤路由（根据权限）
function filterRoutes(routes) {
  const filteredRoutes = []

  routes.forEach(route => {
    const tmp = { ...route }

    // 跳过重定向路由
    if (tmp.redirect && !tmp.component) {
      return
    }

    // 检查路由权限
    if (hasPermission(tmp)) {
      // 如果有子路由，递归过滤
      if (tmp.children && tmp.children.length > 0) {
        tmp.children = filterRoutes(tmp.children)

        // 对于有子路由的情况，如果只有一个子路由且不强制显示父级，则提升子路由
        if (tmp.children.length === 1 && !tmp.alwaysShow) {
          const child = tmp.children[0]
          // 如果子路由没有自己的子路由，则将父路由的信息合并到子路由
          if (!child.children || child.children.length === 0) {
            const mergedRoute = {
              ...tmp,
              ...child,
              meta: child.meta || tmp.meta,
              path: child.path || tmp.path
            }
            if (!mergedRoute.meta?.hidden) {
              filteredRoutes.push(mergedRoute)
            }
            return
          }
        }

        // 如果过滤后没有子路由，且当前路由需要子路由才显示，则不显示
        if (tmp.children.length === 0 && !tmp.alwaysShow) {
          return
        }
      }

      // 不显示隐藏的路由
      if (!tmp.meta?.hidden) {
        filteredRoutes.push(tmp)
      }
    }
  })

  return filteredRoutes
}

// 检查权限
function hasPermission(route) {
  if (route.meta && route.meta.permission) {
    return userStore.hasPermission(route.meta.permission)
  }
  return true
}

// 切换侧边栏
function toggleSidebar() {
  isCollapse.value = !isCollapse.value
}

// 处理用户下拉菜单命令
function handleCommand(command) {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'changePassword':
      showChangePassword.value = true
      break
    case 'logout':
      handleLogout()
      break
  }
}

// 处理退出登录
function handleLogout() {
  ElMessageBox.confirm(
    '确定要退出登录吗？',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    userStore.logout()
  }).catch(() => {
    // 用户取消
  })
}

// 处理密码修改成功
function handlePasswordChanged() {
  ElMessage.success('密码修改成功，请重新登录')
  setTimeout(() => {
    userStore.logout()
  }, 1500)
}

// 监听路由变化，自动收起移动端侧边栏
watch(route, () => {
  if (window.innerWidth <= 768) {
    isCollapse.value = true
  }
})
</script>

<style lang="scss" scoped>
.layout-container {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

.full-height {
  height: 100%;
}

.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.sidebar {
  background: #304156;
  transition: width 0.3s;

  .logo {
    display: flex;
    align-items: center;
    padding: 16px;
    background: #2b3a4b;

    .logo-icon {
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .logo-text {
      margin-left: 12px;
      font-size: 18px;
      font-weight: 600;
      color: #fff;
    }
  }

  .sidebar-menu {
    border-right: none !important;
    background: #304156 !important;

    :deep(.el-menu-item) {
      color: #bfcbd9 !important;
      background: #304156 !important;

      &:hover {
        background: #263445 !important;
        color: #fff !important;
      }

      &.is-active {
        background: #409eff !important;
        color: #fff !important;
      }
    }

    :deep(.el-sub-menu__title) {
      color: #bfcbd9 !important;
      background: #304156 !important;

      &:hover {
        background: #263445 !important;
        color: #fff !important;
      }
    }

    :deep(.el-sub-menu .el-menu-item) {
      background: #1f2d3d !important;
      color: #bfcbd9 !important;

      &:hover {
        background: #001528 !important;
        color: #fff !important;
      }

      &.is-active {
        background: #409eff !important;
        color: #fff !important;
      }
    }

    :deep(.el-sub-menu__icon-arrow) {
      color: #bfcbd9 !important;
    }

    // 确保文字显示
    :deep(.el-menu-item span),
    :deep(.el-sub-menu__title span) {
      color: inherit !important;
      display: inline !important;
    }

    :deep(.el-icon) {
      color: inherit !important;
    }

    // 确保文字显示
    :deep(.el-menu-item span),
    :deep(.el-sub-menu__title span) {
      color: inherit !important;
      display: inline !important;
    }

    :deep(.el-icon) {
      color: inherit !important;
    }
  }
}

.header {
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  height: 60px;
  flex-shrink: 0;

  .header-left {
    display: flex;
    align-items: center;

    .collapse-btn {
      margin-right: 20px;
      font-size: 18px;
    }
  }

  .header-right {
    .user-info {
      display: flex;
      align-items: center;
      cursor: pointer;
      padding: 8px 12px;
      border-radius: 4px;
      transition: background 0.3s;

      &:hover {
        background: #f5f7fa;
      }

      .username {
        margin: 0 8px;
        font-size: 14px;
        color: #606266;
      }

      .arrow-down {
        font-size: 12px;
        color: #909399;
      }
    }
  }
}

.main-content {
  background: #f0f2f5;
  padding: 20px;
  overflow-y: auto;
  flex: 1;
  min-height: 0; /* 允许flex子项收缩 */
}

// 响应式设计
@media (max-width: 768px) {
  .header {
    padding: 0 10px;

    .header-left {
      .collapse-btn {
        margin-right: 10px;
      }
    }
  }

  .main-content {
    padding: 10px;
  }
}
</style>
