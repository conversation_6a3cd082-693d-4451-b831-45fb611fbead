<template>
  <div class="profile">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1 class="page-title">个人中心</h1>
      <p class="page-description">管理您的个人信息和账户设置</p>
    </div>

    <el-row :gutter="20">
      <!-- 用户信息卡片 -->
      <el-col :xs="24" :md="8">
        <el-card class="user-card">
          <div class="user-info">
            <div class="avatar-section">
              <el-avatar :size="100" :src="userStore.avatar">
                <el-icon><User /></el-icon>
              </el-avatar>
              <el-button type="text" class="change-avatar-btn">
                更换头像
              </el-button>
            </div>
            
            <div class="user-details">
              <h3>{{ userStore.userInfo?.username }}</h3>
              <p>{{ userStore.userInfo?.email }}</p>
              
              <div class="user-roles">
                <el-tag
                  v-for="role in userStore.roles"
                  :key="role.id"
                  type="primary"
                  size="small"
                  class="role-tag"
                >
                  {{ role.name }}
                </el-tag>
              </div>
            </div>
          </div>
          
          <el-divider />
          
          <div class="user-stats">
            <div class="stat-item">
              <div class="stat-value">{{ formatDate(userStore.userInfo?.created_at) }}</div>
              <div class="stat-label">注册时间</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ formatDate(userStore.userInfo?.last_login) }}</div>
              <div class="stat-label">最后登录</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 设置面板 -->
      <el-col :xs="24" :md="16">
        <el-card class="settings-card">
          <el-tabs v-model="activeTab" class="settings-tabs">
            <!-- 基本信息 -->
            <el-tab-pane label="基本信息" name="basic">
              <el-form
                ref="basicFormRef"
                :model="basicForm"
                :rules="basicRules"
                label-width="100px"
                class="basic-form"
              >
                <el-form-item label="用户名" prop="username">
                  <el-input
                    v-model="basicForm.username"
                    placeholder="请输入用户名"
                  />
                </el-form-item>
                
                <el-form-item label="邮箱" prop="email">
                  <el-input
                    v-model="basicForm.email"
                    placeholder="请输入邮箱"
                  />
                </el-form-item>
                
                <el-form-item label="头像URL" prop="avatar">
                  <el-input
                    v-model="basicForm.avatar"
                    placeholder="请输入头像URL"
                  />
                </el-form-item>
                
                <el-form-item>
                  <el-button
                    type="primary"
                    :loading="basicLoading"
                    @click="handleUpdateBasic"
                  >
                    保存修改
                  </el-button>
                </el-form-item>
              </el-form>
            </el-tab-pane>

            <!-- 修改密码 -->
            <el-tab-pane label="修改密码" name="password">
              <el-form
                ref="passwordFormRef"
                :model="passwordForm"
                :rules="passwordRules"
                label-width="100px"
                class="password-form"
              >
                <el-form-item label="当前密码" prop="oldPassword">
                  <el-input
                    v-model="passwordForm.oldPassword"
                    type="password"
                    placeholder="请输入当前密码"
                    show-password
                  />
                </el-form-item>
                
                <el-form-item label="新密码" prop="newPassword">
                  <el-input
                    v-model="passwordForm.newPassword"
                    type="password"
                    placeholder="请输入新密码"
                    show-password
                  />
                </el-form-item>
                
                <el-form-item label="确认密码" prop="confirmPassword">
                  <el-input
                    v-model="passwordForm.confirmPassword"
                    type="password"
                    placeholder="请再次输入新密码"
                    show-password
                  />
                </el-form-item>
                
                <el-form-item>
                  <el-button
                    type="primary"
                    :loading="passwordLoading"
                    @click="handleChangePassword"
                  >
                    修改密码
                  </el-button>
                </el-form-item>
              </el-form>
            </el-tab-pane>

            <!-- 权限信息 -->
            <el-tab-pane label="权限信息" name="permissions">
              <div class="permissions-section">
                <h4>我的角色</h4>
                <div class="roles-list">
                  <el-tag
                    v-for="role in userStore.roles"
                    :key="role.id"
                    type="primary"
                    size="large"
                    class="role-item"
                  >
                    {{ role.name }}
                  </el-tag>
                </div>
                
                <h4>我的权限</h4>
                <div class="permissions-list">
                  <el-tag
                    v-for="permission in userStore.permissions"
                    :key="permission.id"
                    size="small"
                    class="permission-item"
                  >
                    {{ permission.name }}
                  </el-tag>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'

const userStore = useUserStore()

// 响应式数据
const activeTab = ref('basic')
const basicLoading = ref(false)
const passwordLoading = ref(false)
const basicFormRef = ref()
const passwordFormRef = ref()

// 基本信息表单
const basicForm = reactive({
  username: '',
  email: '',
  avatar: ''
})

// 密码表单
const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 基本信息验证规则
const basicRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 50, message: '用户名长度在3到50个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ]
}

// 密码验证规则
const passwordRules = {
  oldPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 初始化表单数据
function initFormData() {
  if (userStore.userInfo) {
    Object.assign(basicForm, {
      username: userStore.userInfo.username,
      email: userStore.userInfo.email,
      avatar: userStore.userInfo.avatar || ''
    })
  }
}

// 更新基本信息
async function handleUpdateBasic() {
  if (!basicFormRef.value) return
  
  try {
    await basicFormRef.value.validate()
    
    basicLoading.value = true
    
    await userStore.updateUserInfo(basicForm)
    
    ElMessage.success('基本信息更新成功')
  } catch (error) {
    console.error('更新基本信息失败:', error)
  } finally {
    basicLoading.value = false
  }
}

// 修改密码
async function handleChangePassword() {
  if (!passwordFormRef.value) return
  
  try {
    await passwordFormRef.value.validate()
    
    passwordLoading.value = true
    
    await userStore.changePassword({
      old_password: passwordForm.oldPassword,
      new_password: passwordForm.newPassword
    })
    
    // 清空表单
    Object.assign(passwordForm, {
      oldPassword: '',
      newPassword: '',
      confirmPassword: ''
    })
    
    if (passwordFormRef.value) {
      passwordFormRef.value.resetFields()
    }
    
    ElMessage.success('密码修改成功，请重新登录')
    
    // 延迟退出登录
    setTimeout(() => {
      userStore.logout()
    }, 1500)
  } catch (error) {
    console.error('修改密码失败:', error)
  } finally {
    passwordLoading.value = false
  }
}

// 格式化日期
function formatDate(dateString) {
  if (!dateString) return '暂无'
  return new Date(dateString).toLocaleString('zh-CN')
}

onMounted(() => {
  initFormData()
})
</script>

<style lang="scss" scoped>
.profile {
  .user-card {
    .user-info {
      text-align: center;
      
      .avatar-section {
        margin-bottom: 20px;
        
        .change-avatar-btn {
          display: block;
          margin: 12px auto 0;
          color: #409eff;
        }
      }
      
      .user-details {
        h3 {
          margin: 0 0 8px 0;
          color: #2c3e50;
        }
        
        p {
          margin: 0 0 16px 0;
          color: #7f8c8d;
        }
        
        .role-tag {
          margin: 0 4px 4px 0;
        }
      }
    }
    
    .user-stats {
      display: flex;
      justify-content: space-around;
      
      .stat-item {
        text-align: center;
        
        .stat-value {
          font-size: 14px;
          color: #2c3e50;
          margin-bottom: 4px;
        }
        
        .stat-label {
          font-size: 12px;
          color: #7f8c8d;
        }
      }
    }
  }
  
  .settings-card {
    .settings-tabs {
      :deep(.el-tabs__content) {
        padding-top: 20px;
      }
    }
    
    .basic-form,
    .password-form {
      max-width: 500px;
    }
    
    .permissions-section {
      h4 {
        margin: 0 0 16px 0;
        color: #2c3e50;
      }
      
      .roles-list {
        margin-bottom: 30px;
        
        .role-item {
          margin: 0 8px 8px 0;
        }
      }
      
      .permissions-list {
        .permission-item {
          margin: 0 4px 4px 0;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .user-stats {
    flex-direction: column;
    gap: 16px;
  }
}
</style>
