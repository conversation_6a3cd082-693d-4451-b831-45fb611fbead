<template>
  <div class="role-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1 class="page-title">角色管理</h1>
      <p class="page-description">管理系统角色和权限分配</p>
    </div>

    <!-- 搜索和操作栏 -->
    <div class="table-container">
      <div class="table-header">
        <div class="table-toolbar">
          <div class="toolbar-left">
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索角色名称或描述"
              style="width: 250px"
              clearable
              @keyup.enter="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            
            <el-select
              v-model="searchForm.status"
              placeholder="状态"
              style="width: 120px"
              clearable
              @change="handleSearch"
            >
              <el-option label="启用" :value="1" />
              <el-option label="禁用" :value="0" />
            </el-select>
            
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </div>
          
          <div class="toolbar-right">
            <el-button
              v-if="userStore.hasPermission('role:create')"
              type="primary"
              @click="handleAdd"
            >
              <el-icon><Plus /></el-icon>
              新增角色
            </el-button>
          </div>
        </div>
      </div>

      <!-- 角色表格 -->
      <el-table
        v-loading="loading"
        :data="roleList"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="角色名称" />
        <el-table-column prop="description" label="描述" min-width="200" />
        
        <el-table-column label="用户数" width="100">
          <template #default="{ row }">
            <el-tag type="info" size="small">
              {{ row.user_count || 0 }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="权限数" width="100">
          <template #default="{ row }">
            <el-tag type="success" size="small">
              {{ row.permission_count || 0 }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button
                v-if="userStore.hasPermission('role:update')"
                type="primary"
                size="small"
                @click="handleEdit(row)"
              >
                编辑
              </el-button>
              
              <el-button
                v-if="userStore.hasPermission('role:update')"
                type="warning"
                size="small"
                @click="handleAssignPermissions(row)"
              >
                分配权限
              </el-button>
              
              <el-button
                v-if="userStore.hasPermission('role:delete')"
                type="danger"
                size="small"
                @click="handleDelete(row)"
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 角色表单对话框 -->
    <role-form-dialog
      v-model="showRoleDialog"
      :role-data="currentRole"
      @success="handleFormSuccess"
    />

    <!-- 权限分配对话框 -->
    <permission-assign-dialog
      v-model="showPermissionDialog"
      :role-data="currentRole"
      @success="handleFormSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { request } from '@/utils/request'
import { ElMessage, ElMessageBox } from 'element-plus'
import RoleFormDialog from './components/RoleFormDialog.vue'
import PermissionAssignDialog from './components/PermissionAssignDialog.vue'

const userStore = useUserStore()

// 响应式数据
const loading = ref(false)
const roleList = ref([])
const showRoleDialog = ref(false)
const showPermissionDialog = ref(false)
const currentRole = ref(null)

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: ''
})

// 分页信息
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 获取角色列表
async function getRoleList() {
  try {
    loading.value = true
    
    const params = {
      page: pagination.page,
      page_size: pagination.pageSize,
      keyword: searchForm.keyword,
      status: searchForm.status
    }
    
    const response = await request.get('/roles', params)
    
    roleList.value = response.roles || []
    pagination.total = response.total || 0
  } catch (error) {
    console.error('获取角色列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 搜索
function handleSearch() {
  pagination.page = 1
  getRoleList()
}

// 重置搜索
function handleReset() {
  Object.assign(searchForm, {
    keyword: '',
    status: ''
  })
  pagination.page = 1
  getRoleList()
}

// 新增角色
function handleAdd() {
  currentRole.value = null
  showRoleDialog.value = true
}

// 编辑角色
function handleEdit(role) {
  currentRole.value = { ...role }
  showRoleDialog.value = true
}

// 分配权限
function handleAssignPermissions(role) {
  currentRole.value = { ...role }
  showPermissionDialog.value = true
}

// 删除角色
function handleDelete(role) {
  ElMessageBox.confirm(
    `确定要删除角色 "${role.name}" 吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await request.delete(`/roles/${role.id}`)
      ElMessage.success('删除成功')
      getRoleList()
    } catch (error) {
      console.error('删除角色失败:', error)
    }
  }).catch(() => {
    // 用户取消删除
  })
}

// 表单提交成功
function handleFormSuccess() {
  getRoleList()
}

// 分页大小改变
function handleSizeChange(size) {
  pagination.pageSize = size
  pagination.page = 1
  getRoleList()
}

// 当前页改变
function handleCurrentChange(page) {
  pagination.page = page
  getRoleList()
}

// 格式化日期
function formatDate(dateString) {
  if (!dateString) return '暂无'
  return new Date(dateString).toLocaleString('zh-CN')
}

onMounted(() => {
  getRoleList()
})
</script>

<style lang="scss" scoped>
.role-management {
  .table-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    .toolbar-left {
      display: flex;
      gap: 12px;
      align-items: center;
    }
  }
  
  .action-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }
  
  .pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .table-toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch !important;
    
    .toolbar-left {
      flex-direction: column;
      gap: 8px;
    }
  }
  
  .action-buttons {
    flex-direction: column;
    
    .el-button {
      width: 100%;
    }
  }
}
</style>
