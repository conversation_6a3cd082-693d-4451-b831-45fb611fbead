<template>
  <div class="user-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1 class="page-title">用户管理</h1>
      <p class="page-description">管理系统用户信息和权限</p>
    </div>

    <!-- 搜索和操作栏 -->
    <div class="table-container">
      <div class="table-header">
        <div class="table-toolbar">
          <div class="toolbar-left">
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索用户名或邮箱"
              style="width: 250px"
              clearable
              @keyup.enter="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            
            <el-select
              v-model="searchForm.status"
              placeholder="状态"
              style="width: 120px"
              clearable
              @change="handleSearch"
            >
              <el-option label="启用" :value="1" />
              <el-option label="禁用" :value="0" />
            </el-select>
            
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </div>
          
          <div class="toolbar-right">
            <el-button
              v-if="userStore.hasPermission('user:create')"
              type="primary"
              @click="handleAdd"
            >
              <el-icon><Plus /></el-icon>
              新增用户
            </el-button>
          </div>
        </div>
      </div>

      <!-- 用户表格 -->
      <el-table
        v-loading="loading"
        :data="userList"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        
        <el-table-column label="头像" width="80">
          <template #default="{ row }">
            <el-avatar :size="40" :src="row.avatar">
              <el-icon><User /></el-icon>
            </el-avatar>
          </template>
        </el-table-column>
        
        <el-table-column prop="username" label="用户名" />
        <el-table-column prop="email" label="邮箱" />
        
        <el-table-column label="角色" width="200">
          <template #default="{ row }">
            <el-tag
              v-for="role in row.roles"
              :key="role.id"
              size="small"
              class="role-tag"
            >
              {{ role.name }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="最后登录" width="180">
          <template #default="{ row }">
            {{ formatDate(row.last_login) }}
          </template>
        </el-table-column>
        
        <el-table-column label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button
                v-if="userStore.hasPermission('user:update')"
                type="primary"
                size="small"
                @click="handleEdit(row)"
              >
                编辑
              </el-button>
              
              <el-button
                v-if="userStore.hasPermission('user:delete') && row.id !== userStore.userInfo?.id"
                type="danger"
                size="small"
                @click="handleDelete(row)"
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 用户表单对话框 -->
    <user-form-dialog
      v-model="showUserDialog"
      :user-data="currentUser"
      @success="handleFormSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { request } from '@/utils/request'
import { ElMessage, ElMessageBox } from 'element-plus'
import UserFormDialog from './components/UserFormDialog.vue'

const userStore = useUserStore()

// 响应式数据
const loading = ref(false)
const userList = ref([])
const showUserDialog = ref(false)
const currentUser = ref(null)

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: ''
})

// 分页信息
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 获取用户列表
async function getUserList() {
  try {
    loading.value = true
    
    const params = {
      page: pagination.page,
      page_size: pagination.pageSize,
      keyword: searchForm.keyword,
      status: searchForm.status
    }
    
    const response = await request.get('/users', params)
    
    userList.value = response.users || []
    pagination.total = response.total || 0
  } catch (error) {
    console.error('获取用户列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 搜索
function handleSearch() {
  pagination.page = 1
  getUserList()
}

// 重置搜索
function handleReset() {
  Object.assign(searchForm, {
    keyword: '',
    status: ''
  })
  pagination.page = 1
  getUserList()
}

// 新增用户
function handleAdd() {
  currentUser.value = null
  showUserDialog.value = true
}

// 编辑用户
function handleEdit(user) {
  currentUser.value = { ...user }
  showUserDialog.value = true
}

// 删除用户
function handleDelete(user) {
  ElMessageBox.confirm(
    `确定要删除用户 "${user.username}" 吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await request.delete(`/users/${user.id}`)
      ElMessage.success('删除成功')
      getUserList()
    } catch (error) {
      console.error('删除用户失败:', error)
    }
  }).catch(() => {
    // 用户取消删除
  })
}

// 表单提交成功
function handleFormSuccess() {
  getUserList()
}

// 分页大小改变
function handleSizeChange(size) {
  pagination.pageSize = size
  pagination.page = 1
  getUserList()
}

// 当前页改变
function handleCurrentChange(page) {
  pagination.page = page
  getUserList()
}

// 格式化日期
function formatDate(dateString) {
  if (!dateString) return '暂无'
  return new Date(dateString).toLocaleString('zh-CN')
}

onMounted(() => {
  getUserList()
})
</script>

<style lang="scss" scoped>
.user-management {
  .table-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    .toolbar-left {
      display: flex;
      gap: 12px;
      align-items: center;
    }
  }
  
  .role-tag {
    margin-right: 4px;
    margin-bottom: 4px;
  }
  
  .action-buttons {
    display: flex;
    gap: 8px;
  }
  
  .pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .table-toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch !important;
    
    .toolbar-left {
      flex-direction: column;
      gap: 8px;
    }
  }
  
  .action-buttons {
    flex-direction: column;
    
    .el-button {
      width: 100%;
    }
  }
}
</style>
