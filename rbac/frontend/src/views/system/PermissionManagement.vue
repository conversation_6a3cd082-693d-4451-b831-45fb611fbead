<template>
  <div class="permission-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1 class="page-title">权限管理</h1>
      <p class="page-description">管理系统权限和菜单结构</p>
    </div>

    <!-- 搜索和操作栏 -->
    <div class="table-container">
      <div class="table-header">
        <div class="table-toolbar">
          <div class="toolbar-left">
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索权限名称或代码"
              style="width: 250px"
              clearable
              @keyup.enter="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            
            <el-select
              v-model="searchForm.type"
              placeholder="权限类型"
              style="width: 120px"
              clearable
              @change="handleSearch"
            >
              <el-option label="菜单" value="menu" />
              <el-option label="按钮" value="button" />
              <el-option label="接口" value="api" />
            </el-select>
            
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
            
            <el-button type="success" @click="toggleTreeView">
              <el-icon><List /></el-icon>
              {{ showTree ? '列表视图' : '树形视图' }}
            </el-button>
          </div>
          
          <div class="toolbar-right">
            <el-button
              v-if="userStore.hasPermission('permission:create')"
              type="primary"
              @click="handleAdd"
            >
              <el-icon><Plus /></el-icon>
              新增权限
            </el-button>
          </div>
        </div>
      </div>

      <!-- 权限表格 -->
      <el-table
        v-loading="loading"
        :data="permissionList"
        stripe
        style="width: 100%"
        row-key="id"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :default-expand-all="false"
      >
        <el-table-column prop="id" label="ID" width="80" />
        
        <el-table-column prop="name" label="权限名称" min-width="150">
          <template #default="{ row }">
            <el-icon v-if="row.icon" style="margin-right: 8px;">
              <component :is="row.icon" />
            </el-icon>
            {{ row.name }}
          </template>
        </el-table-column>
        
        <el-table-column prop="code" label="权限代码" min-width="150" />
        
        <el-table-column label="类型" width="100">
          <template #default="{ row }">
            <el-tag
              :type="getTypeTagType(row.type)"
              size="small"
            >
              {{ getTypeText(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="description" label="描述" min-width="200" />
        
        <el-table-column prop="path" label="路径" min-width="150" />
        
        <el-table-column label="排序" width="80">
          <template #default="{ row }">
            {{ row.sort || 0 }}
          </template>
        </el-table-column>
        
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button
                v-if="userStore.hasPermission('permission:update')"
                type="primary"
                size="small"
                @click="handleEdit(row)"
              >
                编辑
              </el-button>
              
              <el-button
                v-if="userStore.hasPermission('permission:delete')"
                type="danger"
                size="small"
                @click="handleDelete(row)"
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div v-if="!showTree" class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 权限表单对话框 -->
    <permission-form-dialog
      v-model="showPermissionDialog"
      :permission-data="currentPermission"
      @success="handleFormSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useUserStore } from '@/stores/user'
import { request } from '@/utils/request'
import { ElMessage, ElMessageBox } from 'element-plus'
import PermissionFormDialog from './components/PermissionFormDialog.vue'

const userStore = useUserStore()

// 响应式数据
const loading = ref(false)
const permissionList = ref([])
const showPermissionDialog = ref(false)
const currentPermission = ref(null)
const showTree = ref(false)

// 搜索表单
const searchForm = reactive({
  keyword: '',
  type: ''
})

// 分页信息
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 获取权限列表
async function getPermissionList() {
  try {
    loading.value = true
    
    const params = showTree.value ? 
      { tree: 'true' } : 
      {
        page: pagination.page,
        page_size: pagination.pageSize,
        keyword: searchForm.keyword,
        type: searchForm.type
      }
    
    const response = await request.get('/permissions', params)
    
    if (showTree.value) {
      permissionList.value = response || []
    } else {
      permissionList.value = response.permissions || []
      pagination.total = response.total || 0
    }
  } catch (error) {
    console.error('获取权限列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 搜索
function handleSearch() {
  if (showTree.value) {
    getPermissionList()
  } else {
    pagination.page = 1
    getPermissionList()
  }
}

// 重置搜索
function handleReset() {
  Object.assign(searchForm, {
    keyword: '',
    type: ''
  })
  pagination.page = 1
  getPermissionList()
}

// 切换视图
function toggleTreeView() {
  showTree.value = !showTree.value
  getPermissionList()
}

// 新增权限
function handleAdd() {
  currentPermission.value = null
  showPermissionDialog.value = true
}

// 编辑权限
function handleEdit(permission) {
  currentPermission.value = { ...permission }
  showPermissionDialog.value = true
}

// 删除权限
function handleDelete(permission) {
  ElMessageBox.confirm(
    `确定要删除权限 "${permission.name}" 吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await request.delete(`/permissions/${permission.id}`)
      ElMessage.success('删除成功')
      getPermissionList()
    } catch (error) {
      console.error('删除权限失败:', error)
    }
  }).catch(() => {
    // 用户取消删除
  })
}

// 表单提交成功
function handleFormSuccess() {
  getPermissionList()
}

// 分页大小改变
function handleSizeChange(size) {
  pagination.pageSize = size
  pagination.page = 1
  getPermissionList()
}

// 当前页改变
function handleCurrentChange(page) {
  pagination.page = page
  getPermissionList()
}

// 获取类型标签类型
function getTypeTagType(type) {
  const typeMap = {
    menu: 'primary',
    button: 'success',
    api: 'warning'
  }
  return typeMap[type] || 'info'
}

// 获取类型文本
function getTypeText(type) {
  const typeMap = {
    menu: '菜单',
    button: '按钮',
    api: '接口'
  }
  return typeMap[type] || type
}

// 格式化日期
function formatDate(dateString) {
  if (!dateString) return '暂无'
  return new Date(dateString).toLocaleString('zh-CN')
}

onMounted(() => {
  getPermissionList()
})
</script>

<style lang="scss" scoped>
.permission-management {
  .table-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    .toolbar-left {
      display: flex;
      gap: 12px;
      align-items: center;
    }
  }
  
  .action-buttons {
    display: flex;
    gap: 8px;
  }
  
  .pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .table-toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch !important;
    
    .toolbar-left {
      flex-direction: column;
      gap: 8px;
    }
  }
  
  .action-buttons {
    flex-direction: column;
    
    .el-button {
      width: 100%;
    }
  }
}
</style>
