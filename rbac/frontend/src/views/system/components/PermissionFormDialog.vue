<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑权限' : '新增权限'"
    width="600px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="权限名称" prop="name">
            <el-input
              v-model="form.name"
              placeholder="请输入权限名称"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="权限代码" prop="code">
            <el-input
              v-model="form.code"
              placeholder="如: user:create"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="权限类型" prop="type">
            <el-select
              v-model="form.type"
              placeholder="请选择权限类型"
              style="width: 100%"
            >
              <el-option label="菜单" value="menu" />
              <el-option label="按钮" value="button" />
              <el-option label="接口" value="api" />
            </el-select>
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="父权限" prop="parent_id">
            <el-tree-select
              v-model="form.parent_id"
              :data="permissionTree"
              :props="{ label: 'name', value: 'id' }"
              placeholder="请选择父权限"
              clearable
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item label="描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="2"
          placeholder="请输入权限描述"
        />
      </el-form-item>
      
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="图标" prop="icon">
            <el-input
              v-model="form.icon"
              placeholder="如: User"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="8">
          <el-form-item label="路径" prop="path">
            <el-input
              v-model="form.path"
              placeholder="如: /system/users"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="8">
          <el-form-item label="排序" prop="sort">
            <el-input-number
              v-model="form.sort"
              :min="0"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item label="组件路径" prop="component">
        <el-input
          v-model="form.component"
          placeholder="如: @/views/system/UserManagement.vue"
        />
      </el-form-item>
      
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio :label="1">启用</el-radio>
          <el-radio :label="0">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :loading="loading"
          @click="handleSubmit"
        >
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, computed, onMounted } from 'vue'
import { request } from '@/utils/request'
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  permissionData: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

const formRef = ref()
const loading = ref(false)
const permissionTree = ref([])

// 对话框显示状态
const dialogVisible = ref(false)

// 是否为编辑模式
const isEdit = computed(() => !!props.permissionData)

// 表单数据
const form = reactive({
  name: '',
  code: '',
  type: 'menu',
  description: '',
  parent_id: null,
  sort: 0,
  icon: '',
  path: '',
  component: '',
  status: 1
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入权限名称', trigger: 'blur' },
    { min: 2, max: 50, message: '权限名称长度在2到50个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入权限代码', trigger: 'blur' },
    { min: 2, max: 100, message: '权限代码长度在2到100个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择权限类型', trigger: 'change' }
  ]
}

// 监听props变化
watch(() => props.modelValue, (val) => {
  dialogVisible.value = val
})

// 监听对话框状态变化
watch(dialogVisible, (val) => {
  emit('update:modelValue', val)
  if (val) {
    initForm()
    getPermissionTree()
  } else {
    resetForm()
  }
})

// 初始化表单
function initForm() {
  if (props.permissionData) {
    Object.assign(form, {
      name: props.permissionData.name,
      code: props.permissionData.code,
      type: props.permissionData.type,
      description: props.permissionData.description || '',
      parent_id: props.permissionData.parent_id,
      sort: props.permissionData.sort || 0,
      icon: props.permissionData.icon || '',
      path: props.permissionData.path || '',
      component: props.permissionData.component || '',
      status: props.permissionData.status
    })
  }
}

// 重置表单
function resetForm() {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  Object.assign(form, {
    name: '',
    code: '',
    type: 'menu',
    description: '',
    parent_id: null,
    sort: 0,
    icon: '',
    path: '',
    component: '',
    status: 1
  })
}

// 获取权限树
async function getPermissionTree() {
  try {
    const response = await request.get('/permissions', { tree: 'true' })
    permissionTree.value = response || []
  } catch (error) {
    console.error('获取权限树失败:', error)
  }
}

// 关闭对话框
function handleClose() {
  dialogVisible.value = false
}

// 提交表单
async function handleSubmit() {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    loading.value = true
    
    if (isEdit.value) {
      await request.put(`/permissions/${props.permissionData.id}`, form)
      ElMessage.success('权限更新成功')
    } else {
      await request.post('/permissions', form)
      ElMessage.success('权限创建成功')
    }
    
    emit('success')
    handleClose()
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  getPermissionTree()
})
</script>

<style lang="scss" scoped>
.dialog-footer {
  text-align: right;
}
</style>
