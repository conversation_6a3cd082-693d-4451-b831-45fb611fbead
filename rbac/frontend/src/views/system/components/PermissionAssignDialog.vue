<template>
  <el-dialog
    v-model="dialogVisible"
    title="分配权限"
    width="600px"
    :before-close="handleClose"
  >
    <div class="permission-assign">
      <div class="role-info">
        <h4>角色: {{ roleData?.name }}</h4>
        <p>{{ roleData?.description }}</p>
      </div>
      
      <el-divider />
      
      <div class="permission-tree">
        <el-tree
          ref="treeRef"
          :data="permissionTree"
          :props="{ label: 'name', children: 'children' }"
          node-key="id"
          show-checkbox
          :default-checked-keys="checkedPermissions"
          :default-expand-all="true"
          class="permission-tree-component"
        >
          <template #default="{ node, data }">
            <div class="tree-node">
              <el-icon v-if="data.icon" style="margin-right: 8px;">
                <component :is="data.icon" />
              </el-icon>
              <span>{{ data.name }}</span>
              <el-tag
                :type="getTypeTagType(data.type)"
                size="small"
                style="margin-left: 8px;"
              >
                {{ getTypeText(data.type) }}
              </el-tag>
            </div>
          </template>
        </el-tree>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :loading="loading"
          @click="handleSubmit"
        >
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue'
import { request } from '@/utils/request'
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  roleData: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

const treeRef = ref()
const loading = ref(false)
const permissionTree = ref([])
const checkedPermissions = ref([])

// 对话框显示状态
const dialogVisible = ref(false)

// 监听props变化
watch(() => props.modelValue, (val) => {
  dialogVisible.value = val
})

// 监听对话框状态变化
watch(dialogVisible, (val) => {
  emit('update:modelValue', val)
  if (val) {
    getPermissionTree()
    getRolePermissions()
  }
})

// 获取权限树
async function getPermissionTree() {
  try {
    const response = await request.get('/permissions', { tree: 'true' })
    permissionTree.value = response || []
  } catch (error) {
    console.error('获取权限树失败:', error)
  }
}

// 获取角色权限
async function getRolePermissions() {
  if (!props.roleData) return
  
  try {
    const response = await request.get(`/roles/${props.roleData.id}`)
    checkedPermissions.value = response.permissions?.map(p => p.id) || []
  } catch (error) {
    console.error('获取角色权限失败:', error)
  }
}

// 获取类型标签类型
function getTypeTagType(type) {
  const typeMap = {
    menu: 'primary',
    button: 'success',
    api: 'warning'
  }
  return typeMap[type] || 'info'
}

// 获取类型文本
function getTypeText(type) {
  const typeMap = {
    menu: '菜单',
    button: '按钮',
    api: '接口'
  }
  return typeMap[type] || type
}

// 关闭对话框
function handleClose() {
  dialogVisible.value = false
}

// 提交表单
async function handleSubmit() {
  if (!props.roleData || !treeRef.value) return
  
  try {
    loading.value = true
    
    // 获取选中的权限ID
    const checkedKeys = treeRef.value.getCheckedKeys()
    const halfCheckedKeys = treeRef.value.getHalfCheckedKeys()
    const permissionIds = [...checkedKeys, ...halfCheckedKeys]
    
    await request.post(`/roles/${props.roleData.id}/permissions`, {
      permission_ids: permissionIds
    })
    
    ElMessage.success('权限分配成功')
    emit('success')
    handleClose()
  } catch (error) {
    console.error('分配权限失败:', error)
  } finally {
    loading.value = false
  }
}
</script>

<style lang="scss" scoped>
.permission-assign {
  .role-info {
    h4 {
      margin: 0 0 8px 0;
      color: #2c3e50;
    }
    
    p {
      margin: 0;
      color: #7f8c8d;
      font-size: 14px;
    }
  }
  
  .permission-tree {
    max-height: 400px;
    overflow-y: auto;
    
    .permission-tree-component {
      :deep(.el-tree-node__content) {
        height: auto;
        padding: 4px 0;
      }
    }
    
    .tree-node {
      display: flex;
      align-items: center;
      flex: 1;
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
