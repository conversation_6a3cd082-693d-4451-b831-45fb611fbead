<template>
  <div class="dashboard">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1 class="page-title">仪表盘</h1>
      <p class="page-description">欢迎使用RBAC权限管理系统</p>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xs="24" :sm="12" :md="6" v-for="stat in stats" :key="stat.title">
        <div class="stat-card">
          <div class="stat-icon" :style="{ background: stat.color }">
            <el-icon :size="24">
              <component :is="stat.icon" />
            </el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stat.value }}</div>
            <div class="stat-title">{{ stat.title }}</div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 图表和信息区域 -->
    <el-row :gutter="20" class="content-row">
      <!-- 用户信息卡片 -->
      <el-col :xs="24" :md="12">
        <el-card class="user-info-card">
          <template #header>
            <div class="card-header">
              <span>用户信息</span>
            </div>
          </template>

          <div class="user-info">
            <el-avatar :size="80" :src="userStore.avatar">
              <el-icon><User /></el-icon>
            </el-avatar>

            <div class="user-details">
              <h3>{{ userStore.userInfo?.username }}</h3>
              <p>{{ userStore.userInfo?.email }}</p>

              <div class="user-roles">
                <el-tag
                  v-for="role in userStore.roles"
                  :key="role.id"
                  type="primary"
                  size="small"
                  class="role-tag"
                >
                  {{ role.name }}
                </el-tag>
              </div>

              <div class="user-meta">
                <p><strong>最后登录:</strong> {{ formatDate(userStore.userInfo?.last_login) }}</p>
                <p><strong>注册时间:</strong> {{ formatDate(userStore.userInfo?.created_at) }}</p>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 快速操作 -->
      <el-col :xs="24" :md="12">
        <el-card class="quick-actions-card">
          <template #header>
            <div class="card-header">
              <span>快速操作</span>
            </div>
          </template>

          <div class="quick-actions">
            <el-button
              v-for="action in quickActions"
              :key="action.name"
              :type="action.type"
              :icon="action.icon"
              @click="handleQuickAction(action)"
              class="action-btn"
            >
              {{ action.name }}
            </el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 系统信息 -->
    <el-row :gutter="20" class="content-row">
      <el-col :span="24">
        <el-card class="system-info-card">
          <template #header>
            <div class="card-header">
              <span>系统信息</span>
            </div>
          </template>

          <el-descriptions :column="2" border>
            <el-descriptions-item label="系统名称">
              RBAC权限管理系统
            </el-descriptions-item>
            <el-descriptions-item label="系统版本">
              v1.0.0
            </el-descriptions-item>
            <el-descriptions-item label="技术栈">
              Go + Vue3 + Element Plus
            </el-descriptions-item>
            <el-descriptions-item label="数据库">
              SQLite
            </el-descriptions-item>
            <el-descriptions-item label="认证方式">
              JWT Token
            </el-descriptions-item>
            <el-descriptions-item label="权限模型">
              RBAC (Role-Based Access Control)
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
      </el-col>
    </el-row>

    <!-- 功能特性 -->
    <el-row :gutter="20" class="content-row">
      <el-col :span="24">
        <el-card class="features-card">
          <template #header>
            <div class="card-header">
              <span>功能特性</span>
            </div>
          </template>

          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="8" v-for="feature in features" :key="feature.title">
              <div class="feature-item">
                <div class="feature-icon">
                  <el-icon :size="32" :color="feature.color">
                    <component :is="feature.icon" />
                  </el-icon>
                </div>
                <h4>{{ feature.title }}</h4>
                <p>{{ feature.description }}</p>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { request } from '@/utils/request'

const router = useRouter()
const userStore = useUserStore()

// 统计数据
const stats = ref([
  {
    title: '用户总数',
    value: 0,
    icon: 'User',
    color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
  },
  {
    title: '角色总数',
    value: 0,
    icon: 'UserFilled',
    color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)'
  },
  {
    title: '权限总数',
    value: 0,
    icon: 'Key',
    color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)'
  },
  {
    title: '在线用户',
    value: 1,
    icon: 'Connection',
    color: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)'
  }
])

// 快速操作
const quickActions = ref([
  {
    name: '用户管理',
    type: 'primary',
    icon: 'User',
    route: '/system/users',
    permission: 'user:read'
  },
  {
    name: '角色管理',
    type: 'success',
    icon: 'UserFilled',
    route: '/system/roles',
    permission: 'role:read'
  },
  {
    name: '权限管理',
    type: 'warning',
    icon: 'Key',
    route: '/system/permissions',
    permission: 'permission:read'
  },
  {
    name: '个人中心',
    type: 'info',
    icon: 'Setting',
    route: '/profile'
  }
])

// 功能特性
const features = ref([
  {
    title: '用户管理',
    description: '完整的用户生命周期管理，支持用户注册、登录、信息维护等功能',
    icon: 'User',
    color: '#409eff'
  },
  {
    title: '角色管理',
    description: '灵活的角色定义和管理，支持角色权限分配和用户角色绑定',
    icon: 'UserFilled',
    color: '#67c23a'
  },
  {
    title: '权限管理',
    description: '细粒度的权限控制，支持菜单权限、操作权限和数据权限',
    icon: 'Key',
    color: '#e6a23c'
  },
  {
    title: 'JWT认证',
    description: '基于JWT的无状态认证机制，安全可靠的身份验证',
    icon: 'Lock',
    color: '#f56c6c'
  },
  {
    title: '响应式设计',
    description: '现代化的响应式界面设计，支持多种设备和屏幕尺寸',
    icon: 'Monitor',
    color: '#909399'
  },
  {
    title: 'RESTful API',
    description: '标准的RESTful API设计，易于集成和扩展',
    icon: 'Connection',
    color: '#606266'
  }
])

// 格式化日期
function formatDate(dateString) {
  if (!dateString) return '暂无'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 处理快速操作
function handleQuickAction(action) {
  if (action.permission && !userStore.hasPermission(action.permission)) {
    ElMessage.warning('权限不足')
    return
  }

  if (action.route) {
    router.push(action.route)
  }
}

// 获取统计数据
async function getStats() {
  try {
    // 这里可以调用实际的API获取统计数据
    // 暂时使用模拟数据
    stats.value[0].value = Math.floor(Math.random() * 100) + 50
    stats.value[1].value = Math.floor(Math.random() * 20) + 5
    stats.value[2].value = Math.floor(Math.random() * 50) + 20
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 过滤有权限的快速操作
const filteredQuickActions = computed(() => {
  return quickActions.value.filter(action => {
    if (action.permission) {
      return userStore.hasPermission(action.permission)
    }
    return true
  })
})

onMounted(() => {
  getStats()
})
</script>

<style lang="scss" scoped>
.dashboard {
  .stats-row {
    margin-bottom: 20px;
  }

  .content-row {
    margin-bottom: 20px;
  }
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  transition: transform 0.3s, box-shadow 0.3s;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
  }

  .stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    color: white;
  }

  .stat-content {
    flex: 1;

    .stat-value {
      font-size: 28px;
      font-weight: 600;
      color: #2c3e50;
      line-height: 1;
    }

    .stat-title {
      font-size: 14px;
      color: #7f8c8d;
      margin-top: 4px;
    }
  }
}

.user-info-card {
  .user-info {
    display: flex;
    align-items: flex-start;
    gap: 20px;

    .user-details {
      flex: 1;

      h3 {
        margin: 0 0 8px 0;
        color: #2c3e50;
      }

      p {
        margin: 0 0 12px 0;
        color: #7f8c8d;
      }

      .user-roles {
        margin-bottom: 16px;

        .role-tag {
          margin-right: 8px;
        }
      }

      .user-meta {
        p {
          margin: 4px 0;
          font-size: 14px;
          color: #606266;
        }
      }
    }
  }
}

.quick-actions-card {
  .quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 12px;

    .action-btn {
      height: 44px;
    }
  }
}

.features-card {
  .feature-item {
    text-align: center;
    padding: 20px;

    .feature-icon {
      margin-bottom: 16px;
    }

    h4 {
      margin: 0 0 8px 0;
      color: #2c3e50;
    }

    p {
      margin: 0;
      color: #7f8c8d;
      font-size: 14px;
      line-height: 1.5;
    }
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

// 响应式设计
@media (max-width: 768px) {
  .user-info {
    flex-direction: column;
    text-align: center;
  }

  .quick-actions {
    grid-template-columns: 1fr;
  }
}
</style>
