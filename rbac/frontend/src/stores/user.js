import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { request } from '@/utils/request'
import router from '@/router'

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref(localStorage.getItem('token') || '')
  const userInfo = ref(null)
  const permissions = ref([])
  const menus = ref([])
  const roles = ref([])

  // 计算属性
  const isLoggedIn = computed(() => !!token.value)
  const username = computed(() => userInfo.value?.username || '')
  const avatar = computed(() => userInfo.value?.avatar || '')

  // 设置token
  const setToken = (newToken) => {
    token.value = newToken
    if (newToken) {
      localStorage.setItem('token', newToken)
    } else {
      localStorage.removeItem('token')
    }
  }

  // 设置用户信息
  const setUserInfo = (info) => {
    userInfo.value = info
    if (info?.roles) {
      roles.value = info.roles
    }
  }

  // 设置权限
  const setPermissions = (perms) => {
    permissions.value = perms || []
  }

  // 设置菜单
  const setMenus = (menuList) => {
    menus.value = menuList || []
  }

  // 检查权限
  const hasPermission = (permission) => {
    if (!permission) return true
    return permissions.value.some(p => p.code === permission)
  }

  // 检查角色
  const hasRole = (role) => {
    if (!role) return true
    return roles.value.some(r => r.name === role)
  }

  // 登录
  const login = async (loginForm) => {
    try {
      const response = await request.post('/auth/login', loginForm)
      
      if (response.token) {
        setToken(response.token)
        setUserInfo(response.user)
        setPermissions(response.permissions)
        
        ElMessage.success('登录成功')
        
        // 获取用户菜单
        await getUserMenus()
        
        // 跳转到首页
        router.push('/')
        
        return response
      }
    } catch (error) {
      console.error('登录失败:', error)
      throw error
    }
  }

  // 注册
  const register = async (registerForm) => {
    try {
      const response = await request.post('/auth/register', registerForm)
      ElMessage.success('注册成功，请登录')
      return response
    } catch (error) {
      console.error('注册失败:', error)
      throw error
    }
  }

  // 获取用户信息
  const getUserInfo = async () => {
    try {
      const response = await request.get('/auth/profile')
      setUserInfo(response.user)
      setPermissions(response.permissions)
      setMenus(response.menus)
      return response
    } catch (error) {
      console.error('获取用户信息失败:', error)
      // 如果获取用户信息失败，清除token
      logout()
      throw error
    }
  }

  // 获取用户菜单
  const getUserMenus = async () => {
    try {
      const response = await request.get('/menus')
      setMenus(response)
      return response
    } catch (error) {
      console.error('获取用户菜单失败:', error)
      return []
    }
  }

  // 修改密码
  const changePassword = async (passwordForm) => {
    try {
      await request.post('/auth/change-password', passwordForm)
      ElMessage.success('密码修改成功')
    } catch (error) {
      console.error('修改密码失败:', error)
      throw error
    }
  }

  // 注销
  const logout = async () => {
    try {
      await request.post('/auth/logout')
    } catch (error) {
      console.error('注销请求失败:', error)
    } finally {
      // 清除本地数据
      setToken('')
      setUserInfo(null)
      setPermissions([])
      setMenus([])
      roles.value = []
      
      // 跳转到登录页
      router.push('/login')
      
      ElMessage.success('已退出登录')
    }
  }

  // 检查登录状态
  const checkLoginStatus = async () => {
    if (token.value) {
      try {
        await getUserInfo()
      } catch (error) {
        // 如果token无效，清除并跳转到登录页
        logout()
      }
    }
  }

  // 更新用户信息
  const updateUserInfo = async (updateData) => {
    try {
      const response = await request.put(`/users/${userInfo.value.id}`, updateData)
      setUserInfo(response)
      ElMessage.success('用户信息更新成功')
      return response
    } catch (error) {
      console.error('更新用户信息失败:', error)
      throw error
    }
  }

  return {
    // 状态
    token,
    userInfo,
    permissions,
    menus,
    roles,
    
    // 计算属性
    isLoggedIn,
    username,
    avatar,
    
    // 方法
    setToken,
    setUserInfo,
    setPermissions,
    setMenus,
    hasPermission,
    hasRole,
    login,
    register,
    getUserInfo,
    getUserMenus,
    changePassword,
    logout,
    checkLoginStatus,
    updateUserInfo
  }
})
