package main

import (
	"fmt"
	"log"
	"math/rand"
	"os"
	"os/exec"
	"runtime"
	"strconv"
	"strings"
	"time"

	"github.com/shirou/gopsutil/cpu"
	"github.com/shirou/gopsutil/mem"
)

func main() {
	// 初始化随机数生成器
	rand.Seed(time.Now().UnixNano())

	fmt.Println("系统资源监控")
	fmt.Println("----------------------")
	fmt.Printf("操作系统: %s\n", runtime.GOOS)
	fmt.Printf("架构: %s\n", runtime.GOARCH)
	fmt.Println()

	// 创建定时器，每2秒检查一次资源使用情况
	ticker := time.NewTicker(2 * time.Second)
	defer ticker.Stop()

	fmt.Println("\n按 Ctrl+C 退出")
	fmt.Println("\n正在监控系统资源...")

	// 是否使用模拟数据
	useSimulatedData := true

	for {
		select {
		case <-ticker.C:
			// 监控CPU
			cpuUsage, err := getCPUUsage()
			if err != nil {
				log.Printf("获取CPU使用率出错: %v", err)
			}

			// 监控内存
			memUsage, err := getMemoryUsage()
			if err != nil {
				log.Printf("获取内存使用率出错: %v", err)
			}

			// 监控GPU
			var gpuStats string
			if useSimulatedData {
				gpuStats = getSimulatedGPUStats()
			} else {
				stats, err := getGPUStats()
				if err != nil {
					gpuStats = "GPU: 未检测到或无法访问GPU"
				} else {
					gpuStats = stats
				}
			}

			// 根据操作系统清屏
			clearScreen()

			// 打印当前统计信息
			fmt.Println("系统资源监控")
			fmt.Println("----------------------")
			fmt.Printf("操作系统: %s\n", runtime.GOOS)
			fmt.Printf("架构: %s\n", runtime.GOARCH)
			fmt.Println()
			fmt.Println("CPU使用率:", cpuUsage)
			fmt.Println("内存使用率:", memUsage)
			fmt.Println(gpuStats)
			fmt.Println("\n按 Ctrl+C 退出")
		}
	}
}

// getCPUUsage 返回当前CPU使用率的格式化字符串
func getCPUUsage() (string, error) {
	percentages, err := cpu.Percent(0, false)
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("%.2f%%", percentages[0]), nil
}

// getMemoryUsage 返回当前内存使用率的格式化字符串
func getMemoryUsage() (string, error) {
	v, err := mem.VirtualMemory()
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("%.2f%% (已用: %.2f GB, 总计: %.2f GB)",
		v.UsedPercent,
		float64(v.Used)/(1024*1024*1024),
		float64(v.Total)/(1024*1024*1024)), nil
}

// getSimulatedGPUStats 返回模拟的NVIDIA GPU信息
func getSimulatedGPUStats() string {
	// 模拟2个GPU
	gpuCount := 2
	var result string
	result = fmt.Sprintf("GPU: %d NVIDIA设备 (模拟数据)\n", gpuCount)

	gpuNames := []string{
		"NVIDIA GeForce RTX 3080",
		"NVIDIA Tesla V100",
	}

	for i := 0; i < gpuCount; i++ {
		// 生成随机使用率 (0-100%)
		gpuUtil := rand.Intn(95) + 5 // 5-99%

		// 模拟内存使用情况
		memTotal := float64(10 + i*6)                       // 10GB for first GPU, 16GB for second
		memUsedPercent := float64(rand.Intn(90)+10) / 100.0 // 10-99%
		memUsed := memTotal * memUsedPercent

		result += fmt.Sprintf("  GPU %d: %s\n", i, gpuNames[i])
		result += fmt.Sprintf("    使用率: %d%%\n", gpuUtil)
		result += fmt.Sprintf("    显存: %.2f%% (已用: %.2f GB, 总计: %.2f GB)\n",
			memUsedPercent*100, memUsed, memTotal)
	}

	return result
}

// getGPUStats 使用nvidia-smi返回NVIDIA GPU信息
func getGPUStats() (string, error) {
	var cmd *exec.Cmd

	if runtime.GOOS == "windows" {
		// 在Windows上，nvidia-smi通常在PATH或Program Files中
		cmd = exec.Command("nvidia-smi", "--query-gpu=index,name,utilization.gpu,memory.used,memory.total", "--format=csv,noheader,nounits")
	} else {
		// 在Linux上，使用相同的命令
		cmd = exec.Command("nvidia-smi", "--query-gpu=index,name,utilization.gpu,memory.used,memory.total", "--format=csv,noheader,nounits")
	}

	output, err := cmd.Output()
	if err != nil {
		return "", fmt.Errorf("执行nvidia-smi失败: %v", err)
	}

	if len(output) == 0 {
		return "GPU: 未检测到NVIDIA GPU", nil
	}

	// 解析输出
	lines := strings.Split(strings.TrimSpace(string(output)), "\n")
	if len(lines) == 0 {
		return "GPU: 未检测到NVIDIA GPU", nil
	}

	var result string
	result = fmt.Sprintf("GPU: %d NVIDIA设备\n", len(lines))

	for _, line := range lines {
		fields := strings.Split(strings.TrimSpace(line), ",")
		if len(fields) < 5 {
			continue
		}

		index := strings.TrimSpace(fields[0])
		name := strings.TrimSpace(fields[1])
		utilization := strings.TrimSpace(fields[2])
		memoryUsed := strings.TrimSpace(fields[3])
		memoryTotal := strings.TrimSpace(fields[4])

		// 将内存值转换为GB
		memUsedFloat, _ := strconv.ParseFloat(memoryUsed, 64)
		memTotalFloat, _ := strconv.ParseFloat(memoryTotal, 64)
		memUsedGB := memUsedFloat / 1024
		memTotalGB := memTotalFloat / 1024
		memPercent := (memUsedFloat / memTotalFloat) * 100

		result += fmt.Sprintf("  GPU %s: %s\n", index, name)
		result += fmt.Sprintf("    使用率: %s%%\n", utilization)
		result += fmt.Sprintf("    显存: %.2f%% (已用: %.2f GB, 总计: %.2f GB)\n",
			memPercent, memUsedGB, memTotalGB)
	}

	return result, nil
}

// clearScreen 根据操作系统清屏
func clearScreen() {
	switch runtime.GOOS {
	case "windows":
		cmd := exec.Command("cmd", "/c", "cls")
		cmd.Stdout = os.Stdout
		cmd.Run()
	case "linux", "darwin":
		fmt.Print("\033[H\033[2J")
	default:
		// 对于不支持的操作系统，只打印换行符
		fmt.Println("\n\n\n\n\n")
	}
}
